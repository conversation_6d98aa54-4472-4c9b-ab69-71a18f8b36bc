<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fallback Interactive Questions</title>
    
    <!-- Styles -->
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- MathQuill -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
    
    <style>
        .container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .question-container {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .question-number {
            background: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
        }
        
        .question-type {
            background: #10b981;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            cursor: pointer;
            font-weight: 600;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .next-btn {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 2rem;
            cursor: pointer;
            font-weight: 600;
            margin-top: 1rem;
        }
        
        .next-btn:hover {
            background: #059669;
        }
        
        .next-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-3xl font-bold mb-6">Test Fallback Interactive Questions</h1>
        
        <div class="mb-4">
            <button class="test-btn" onclick="loadFallbackQuestions()">Load Fallback Questions</button>
            <button class="test-btn" onclick="testSpecificQuestion()">Test Specific Interactive Question</button>
        </div>
        
        <div id="questions-container">
            <!-- Questions will be loaded here -->
        </div>
    </div>
    
    <!-- Question Template -->
    <div id="question-template" style="display: none;">
        <div class="question-container">
            <div class="question-header">
                <div class="question-number">Question <span class="q-num"></span></div>
                <div class="question-type"></div>
            </div>
            
            <div class="question-text"></div>
            
            <div class="answer-area">
                <!-- Multiple Choice -->
                <div id="multiple-choice-options" class="answer-input hidden">
                    <div class="options-container"></div>
                </div>
                
                <!-- Numeric Input -->
                <div id="numeric-input" class="answer-input hidden">
                    <input type="text" id="numeric-answer" class="text-input" placeholder="Enter your answer">
                </div>
                
                <!-- Number Line -->
                <div id="number-line-input" class="answer-input hidden">
                    <div class="number-line-container">
                        <div class="number-line-instruction">
                            <p>Drag the marker to the correct position on the number line</p>
                        </div>
                        <div id="number-line-widget" class="number-line-widget">
                            <div class="number-line-track"></div>
                            <div class="number-line-markers"></div>
                            <div class="number-line-slider" draggable="true"></div>
                            <div class="number-line-value-display"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Drag and Drop -->
                <div id="drag-drop-input" class="answer-input hidden">
                    <div class="drag-drop-container">
                        <div class="drag-drop-instruction">
                            <p>Drag the mathematical elements to complete the equation</p>
                        </div>
                        <div class="drag-elements-bank" id="drag-elements-bank"></div>
                        <div class="drop-zones-container" id="drop-zones-container"></div>
                    </div>
                </div>
                
                <!-- Visual Manipulatives -->
                <div id="visual-manipulatives-input" class="answer-input hidden">
                    <div class="manipulatives-container">
                        <div class="manipulatives-instruction">
                            <p>Click on the fraction segments to show the answer</p>
                        </div>
                        <div id="manipulatives-workspace" class="manipulatives-workspace"></div>
                        <div class="manipulatives-toolbox" id="manipulatives-toolbox"></div>
                    </div>
                </div>
            </div>
            
            <button class="next-btn" onclick="nextQuestion()" disabled>Next Question</button>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="mathInteractiveComponents.js"></script>
    
    <script>
        // Fallback questions with interactive types
        const fallbackQuestions = [
            {
                id: 1,
                type: "multiple-choice",
                topic: "arithmetic",
                question: "What is 25 + 37?",
                options: ["52", "62", "72", "82"],
                correctAnswer: "62",
                points: 2
            },
            {
                id: 2,
                type: "number-line",
                topic: "fractions",
                question: "Position 3/4 on the number line",
                correctAnswer: "0.75",
                points: 2,
                numberLine: { min: 0, max: 1, step: 0.25 }
            },
            {
                id: 3,
                type: "drag-drop",
                topic: "arithmetic",
                question: "Complete the equation: ___ + 5 = 12",
                correctAnswer: "7",
                points: 2,
                dragElements: [
                    { value: "7", type: "number", display: "7" },
                    { value: "3", type: "number", display: "3" },
                    { value: "8", type: "number", display: "8" }
                ],
                dropZones: [
                    { id: "blank1", placeholder: "Drop number here" }
                ]
            },
            {
                id: 4,
                type: "visual-manipulatives",
                topic: "fractions",
                question: "Show 1/2 using the fraction bar",
                correctAnswer: "0.5",
                points: 2,
                manipulativeType: "fractions",
                fractions: [
                    { numerator: 1, denominator: 2 }
                ]
            }
        ];
        
        let currentQuestionIndex = 0;
        let currentQuestions = [];
        
        function loadFallbackQuestions() {
            console.log('Loading fallback questions...');
            currentQuestions = fallbackQuestions;
            currentQuestionIndex = 0;
            displayCurrentQuestion();
        }
        
        function testSpecificQuestion() {
            console.log('Testing specific interactive question...');
            const interactiveQuestion = {
                id: 1,
                type: "number-line",
                topic: "fractions",
                question: "Position 1/2 on the number line",
                correctAnswer: "0.5",
                points: 2,
                numberLine: { min: 0, max: 1, step: 0.1 }
            };
            
            currentQuestions = [interactiveQuestion];
            currentQuestionIndex = 0;
            displayCurrentQuestion();
        }
        
        function displayCurrentQuestion() {
            if (currentQuestionIndex >= currentQuestions.length) {
                document.getElementById('questions-container').innerHTML = '<div class="question-container"><h2>All questions completed!</h2></div>';
                return;
            }
            
            const question = currentQuestions[currentQuestionIndex];
            console.log('Displaying question:', question);
            
            const container = document.getElementById('questions-container');
            const template = document.getElementById('question-template');
            const questionHtml = template.innerHTML;
            
            container.innerHTML = questionHtml;
            
            // Update question info
            document.querySelector('.q-num').textContent = question.id;
            document.querySelector('.question-type').textContent = question.type;
            document.querySelector('.question-text').textContent = question.question;
            
            // Hide all answer inputs
            hideAllAnswerInputs();
            
            // Show appropriate answer type
            showAnswerInput(question);
        }
        
        function hideAllAnswerInputs() {
            const inputs = document.querySelectorAll('.answer-input');
            inputs.forEach(input => input.classList.add('hidden'));
        }
        
        function showAnswerInput(question) {
            console.log('Showing answer input for type:', question.type);
            
            if (question.type === 'multiple-choice') {
                showMultipleChoice(question);
            } else if (question.type === 'numeric') {
                showNumericInput();
            } else if (question.type === 'number-line') {
                showNumberLine(question);
            } else if (question.type === 'drag-drop') {
                showDragDrop(question);
            } else if (question.type === 'visual-manipulatives') {
                showVisualManipulatives(question);
            }
        }
        
        function showMultipleChoice(question) {
            const container = document.getElementById('multiple-choice-options');
            const optionsContainer = container.querySelector('.options-container');
            
            optionsContainer.innerHTML = '';
            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-btn';
                button.textContent = option;
                button.onclick = () => selectOption(button);
                optionsContainer.appendChild(button);
            });
            
            container.classList.remove('hidden');
        }
        
        function showNumericInput() {
            document.getElementById('numeric-input').classList.remove('hidden');
            document.getElementById('numeric-answer').focus();
        }
        
        function showNumberLine(question) {
            console.log('Showing number line for question:', question);
            
            if (window.mathInteractiveComponents) {
                window.mathInteractiveComponents.showNumberLine(question);
                enableNextButton();
            } else {
                console.error('mathInteractiveComponents not available');
                alert('Interactive components not loaded!');
            }
        }
        
        function showDragDrop(question) {
            console.log('Showing drag drop for question:', question);
            
            if (window.mathInteractiveComponents) {
                window.mathInteractiveComponents.showDragDropPuzzle(question);
                enableNextButton();
            } else {
                console.error('mathInteractiveComponents not available');
                alert('Interactive components not loaded!');
            }
        }
        
        function showVisualManipulatives(question) {
            console.log('Showing visual manipulatives for question:', question);
            
            if (window.mathInteractiveComponents) {
                window.mathInteractiveComponents.showVisualManipulatives(question);
                enableNextButton();
            } else {
                console.error('mathInteractiveComponents not available');
                alert('Interactive components not loaded!');
            }
        }
        
        function selectOption(button) {
            // Remove selection from all buttons
            document.querySelectorAll('.option-btn').forEach(btn => btn.classList.remove('selected'));
            // Select clicked button
            button.classList.add('selected');
            enableNextButton();
        }
        
        function enableNextButton() {
            setTimeout(() => {
                document.querySelector('.next-btn').disabled = false;
            }, 500);
        }
        
        function nextQuestion() {
            currentQuestionIndex++;
            displayCurrentQuestion();
        }
        
        // Check components on load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Page loaded');
            console.log('mathInteractiveComponents available:', !!window.mathInteractiveComponents);
            console.log('MathQuill available:', typeof MQ !== 'undefined');
            console.log('jQuery available:', typeof $ !== 'undefined');
        });
    </script>
</body>
</html>
