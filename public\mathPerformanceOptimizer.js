/**
 * Performance Optimizer for Interactive Mathematics Components
 * Ensures sub-3-second generation targets and optimal user experience
 */

class MathPerformanceOptimizer {
  constructor() {
    this.performanceTargets = {
      componentLoad: 500,      // 500ms max for component loading
      interactionResponse: 100, // 100ms max for interaction response
      questionGeneration: 3000, // 3s max for question generation
      renderTime: 200          // 200ms max for rendering
    };
    
    this.cache = new Map();
    this.preloadedComponents = new Set();
    this.performanceMetrics = {
      loadTimes: [],
      responseTimes: [],
      renderTimes: [],
      cacheHits: 0,
      cacheMisses: 0
    };
    
    this.initialize();
  }

  initialize() {
    this.setupPerformanceMonitoring();
    this.preloadCriticalComponents();
    this.optimizeEventListeners();
    this.setupIntersectionObserver();
  }

  /**
   * Setup performance monitoring for all interactive components
   */
  setupPerformanceMonitoring() {
    // Monitor component loading times
    const originalShowMethods = [
      'showNumberLine',
      'showDragDropPuzzle', 
      'showEquationBuilder',
      'showStepByStep',
      'showCoordinateGrid',
      'showVisualManipulatives'
    ];

    if (window.mathInteractiveComponents) {
      originalShowMethods.forEach(methodName => {
        const originalMethod = window.mathInteractiveComponents[methodName];
        if (originalMethod) {
          window.mathInteractiveComponents[methodName] = (...args) => {
            const startTime = performance.now();
            const result = originalMethod.apply(window.mathInteractiveComponents, args);
            const endTime = performance.now();
            
            this.recordLoadTime(methodName, endTime - startTime);
            return result;
          };
        }
      });
    }
  }

  /**
   * Preload critical components to reduce initial load time
   */
  preloadCriticalComponents() {
    // Preload MathQuill if not already loaded
    if (typeof MQ === 'undefined' && !this.preloadedComponents.has('mathquill')) {
      this.preloadMathQuill();
    }

    // Preload common SVG elements for coordinate grids
    this.preloadSVGElements();
    
    // Cache common question templates
    this.cacheQuestionTemplates();
  }

  /**
   * Preload MathQuill library asynchronously
   */
  async preloadMathQuill() {
    if (this.preloadedComponents.has('mathquill')) return;
    
    try {
      const startTime = performance.now();
      
      // Check if jQuery is available
      if (typeof $ === 'undefined') {
        await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js');
      }
      
      // Load MathQuill CSS
      if (!document.querySelector('link[href*="mathquill"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css';
        document.head.appendChild(link);
      }
      
      // Load MathQuill JS
      if (typeof MQ === 'undefined') {
        await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js');
      }
      
      const loadTime = performance.now() - startTime;
      console.log(`MathQuill preloaded in ${loadTime.toFixed(2)}ms`);
      this.preloadedComponents.add('mathquill');
      
    } catch (error) {
      console.warn('Failed to preload MathQuill:', error);
    }
  }

  /**
   * Load script asynchronously
   */
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  /**
   * Preload SVG elements for coordinate grids
   */
  preloadSVGElements() {
    if (this.preloadedComponents.has('svg-elements')) return;
    
    // Create hidden SVG with common elements
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.display = 'none';
    svg.innerHTML = `
      <defs>
        <pattern id="grid-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="1"/>
        </pattern>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                refX="0" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
        </marker>
      </defs>
    `;
    
    document.body.appendChild(svg);
    this.preloadedComponents.add('svg-elements');
  }

  /**
   * Cache common question templates
   */
  cacheQuestionTemplates() {
    const templates = {
      'number-line-basic': {
        type: 'number-line',
        numberLine: { min: 0, max: 10, step: 1 }
      },
      'number-line-fractions': {
        type: 'number-line', 
        numberLine: { min: 0, max: 1, step: 0.25 }
      },
      'drag-drop-arithmetic': {
        type: 'drag-drop',
        dragElements: [
          { value: '5', type: 'number', display: '5' },
          { value: '3', type: 'number', display: '3' },
          { value: '+', type: 'operator', display: '+' }
        ],
        dropZones: [
          { id: 'num1', placeholder: 'First number' },
          { id: 'op', placeholder: 'Operator' },
          { id: 'num2', placeholder: 'Second number' }
        ]
      },
      'coordinate-grid-basic': {
        type: 'coordinate-grid',
        gridConfig: { gridSize: 20 }
      },
      'fraction-bars-basic': {
        type: 'visual-manipulatives',
        manipulativeType: 'fractions',
        fractions: [
          { numerator: 1, denominator: 2 },
          { numerator: 1, denominator: 4 }
        ]
      }
    };

    Object.entries(templates).forEach(([key, template]) => {
      this.cache.set(key, template);
    });
  }

  /**
   * Optimize event listeners using delegation
   */
  optimizeEventListeners() {
    // Use event delegation for better performance
    document.addEventListener('click', this.handleDelegatedClick.bind(this), { passive: true });
    document.addEventListener('keydown', this.handleDelegatedKeydown.bind(this));
    
    // Throttle resize events
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.handleResize();
      }, 150);
    }, { passive: true });
  }

  /**
   * Handle delegated click events
   */
  handleDelegatedClick(event) {
    const target = event.target;
    
    // Handle toolbar buttons
    if (target.classList.contains('toolbar-btn')) {
      this.recordResponseTime('toolbar-click');
    }
    
    // Handle grid tools
    if (target.classList.contains('grid-tool-btn')) {
      this.recordResponseTime('grid-tool-click');
    }
    
    // Handle fraction segments
    if (target.classList.contains('fraction-segment')) {
      this.recordResponseTime('fraction-segment-click');
    }
  }

  /**
   * Handle delegated keyboard events
   */
  handleDelegatedKeydown(event) {
    const target = event.target;
    
    // Handle number line slider
    if (target.classList.contains('number-line-slider')) {
      this.recordResponseTime('number-line-keyboard');
    }
    
    // Handle coordinate grid
    if (target.id === 'grid-svg') {
      this.recordResponseTime('grid-keyboard');
    }
  }

  /**
   * Setup intersection observer for lazy loading
   */
  setupIntersectionObserver() {
    if ('IntersectionObserver' in window) {
      this.intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.lazyLoadComponent(entry.target);
          }
        });
      }, {
        rootMargin: '50px'
      });
    }
  }

  /**
   * Lazy load component when it becomes visible
   */
  lazyLoadComponent(element) {
    const componentType = element.dataset.componentType;
    if (componentType && !element.dataset.loaded) {
      const startTime = performance.now();
      
      // Load component based on type
      switch (componentType) {
        case 'coordinate-grid':
          this.loadCoordinateGrid(element);
          break;
        case 'equation-builder':
          this.loadEquationBuilder(element);
          break;
        // Add other component types as needed
      }
      
      const loadTime = performance.now() - startTime;
      this.recordLoadTime(`lazy-${componentType}`, loadTime);
      element.dataset.loaded = 'true';
    }
  }

  /**
   * Record component load time
   */
  recordLoadTime(component, time) {
    this.performanceMetrics.loadTimes.push({ component, time, timestamp: Date.now() });
    
    if (time > this.performanceTargets.componentLoad) {
      console.warn(`Component ${component} exceeded load time target: ${time.toFixed(2)}ms`);
    }
  }

  /**
   * Record interaction response time
   */
  recordResponseTime(interaction) {
    const time = performance.now() - (this.lastInteractionStart || performance.now());
    this.performanceMetrics.responseTimes.push({ interaction, time, timestamp: Date.now() });
    
    if (time > this.performanceTargets.interactionResponse) {
      console.warn(`Interaction ${interaction} exceeded response time target: ${time.toFixed(2)}ms`);
    }
  }

  /**
   * Get cached question template
   */
  getCachedTemplate(templateKey) {
    if (this.cache.has(templateKey)) {
      this.performanceMetrics.cacheHits++;
      return this.cache.get(templateKey);
    }
    
    this.performanceMetrics.cacheMisses++;
    return null;
  }

  /**
   * Cache question template
   */
  cacheTemplate(templateKey, template) {
    this.cache.set(templateKey, template);
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Optimize responsive behavior
    const components = document.querySelectorAll('.answer-input:not(.hidden)');
    components.forEach(component => {
      if (component.querySelector('.coordinate-grid')) {
        this.optimizeCoordinateGrid(component);
      }
    });
  }

  /**
   * Optimize coordinate grid for current viewport
   */
  optimizeCoordinateGrid(container) {
    const svg = container.querySelector('.grid-svg');
    if (svg) {
      const containerWidth = container.offsetWidth;
      const optimalSize = Math.min(400, containerWidth - 40);
      svg.style.width = `${optimalSize}px`;
      svg.style.height = `${optimalSize}px`;
    }
  }

  /**
   * Get performance report
   */
  getPerformanceReport() {
    const avgLoadTime = this.performanceMetrics.loadTimes.length > 0
      ? this.performanceMetrics.loadTimes.reduce((sum, metric) => sum + metric.time, 0) / this.performanceMetrics.loadTimes.length
      : 0;
      
    const avgResponseTime = this.performanceMetrics.responseTimes.length > 0
      ? this.performanceMetrics.responseTimes.reduce((sum, metric) => sum + metric.time, 0) / this.performanceMetrics.responseTimes.length
      : 0;

    const cacheHitRate = this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses > 0
      ? (this.performanceMetrics.cacheHits / (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses)) * 100
      : 0;

    return {
      averageLoadTime: Math.round(avgLoadTime * 100) / 100,
      averageResponseTime: Math.round(avgResponseTime * 100) / 100,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      totalInteractions: this.performanceMetrics.responseTimes.length,
      preloadedComponents: Array.from(this.preloadedComponents),
      performanceTargets: this.performanceTargets,
      meetsTargets: {
        loadTime: avgLoadTime <= this.performanceTargets.componentLoad,
        responseTime: avgResponseTime <= this.performanceTargets.interactionResponse
      }
    };
  }

  /**
   * Optimize for mobile devices
   */
  optimizeForMobile() {
    if (window.innerWidth <= 768) {
      // Reduce animation complexity
      document.documentElement.style.setProperty('--animation-duration', '0.1s');
      
      // Simplify visual effects
      const style = document.createElement('style');
      style.textContent = `
        @media (max-width: 768px) {
          .drag-element:hover,
          .number-line-slider:hover,
          .toolbar-btn:hover {
            transform: none !important;
          }
          
          .answer-input {
            scroll-behavior: auto;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }
}

// Initialize performance optimizer
window.mathPerformanceOptimizer = new MathPerformanceOptimizer();

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MathPerformanceOptimizer;
}
