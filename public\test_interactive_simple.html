<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Interactive Math Test</title>
    
    <!-- Styles -->
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- MathQuill -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            cursor: pointer;
            font-weight: 600;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-3xl font-bold mb-6">Simple Interactive Math Component Test</h1>
        
        <!-- Number Line Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">Number Line Test</h2>
            <button class="test-btn" onclick="testNumberLine()">Test Number Line</button>
            
            <div id="number-line-input" class="answer-input hidden">
                <div class="number-line-container">
                    <div class="number-line-instruction">
                        <p>Drag the marker to position 0.5 on the number line</p>
                    </div>
                    <div id="number-line-widget" class="number-line-widget">
                        <div class="number-line-track"></div>
                        <div class="number-line-markers"></div>
                        <div class="number-line-slider" draggable="true"></div>
                        <div class="number-line-value-display"></div>
                    </div>
                </div>
            </div>
            
            <div id="number-line-status" class="status" style="display: none;"></div>
        </div>
        
        <!-- Drag Drop Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">Drag & Drop Test</h2>
            <button class="test-btn" onclick="testDragDrop()">Test Drag & Drop</button>
            
            <div id="drag-drop-input" class="answer-input hidden">
                <div class="drag-drop-container">
                    <div class="drag-drop-instruction">
                        <p>Drag the number 7 to complete: ___ + 5 = 12</p>
                    </div>
                    <div class="drag-elements-bank" id="drag-elements-bank"></div>
                    <div class="drop-zones-container" id="drop-zones-container"></div>
                </div>
            </div>
            
            <div id="drag-drop-status" class="status" style="display: none;"></div>
        </div>
        
        <!-- Visual Manipulatives Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">Visual Manipulatives Test</h2>
            <button class="test-btn" onclick="testVisualManipulatives()">Test Fraction Bars</button>
            
            <div id="visual-manipulatives-input" class="answer-input hidden">
                <div class="manipulatives-container">
                    <div class="manipulatives-instruction">
                        <p>Click on fraction segments to show 1/2</p>
                    </div>
                    <div id="manipulatives-workspace" class="manipulatives-workspace"></div>
                    <div class="manipulatives-toolbox" id="manipulatives-toolbox"></div>
                </div>
            </div>
            
            <div id="manipulatives-status" class="status" style="display: none;"></div>
        </div>
        
        <!-- Component Status -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">Component Status</h2>
            <button class="test-btn" onclick="checkComponents()">Check Components</button>
            <div id="component-status" class="status" style="display: none;"></div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="mathInteractiveComponents.js"></script>
    
    <script>
        function showStatus(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }
        
        function testNumberLine() {
            console.log('Testing number line...');
            
            try {
                const question = {
                    type: 'number-line',
                    numberLine: { min: 0, max: 1, step: 0.25 }
                };
                
                if (window.mathInteractiveComponents) {
                    window.mathInteractiveComponents.showNumberLine(question);
                    showStatus('number-line-status', 'Number line component loaded successfully!');
                } else {
                    showStatus('number-line-status', 'mathInteractiveComponents not found!', false);
                }
            } catch (error) {
                console.error('Number line test error:', error);
                showStatus('number-line-status', `Error: ${error.message}`, false);
            }
        }
        
        function testDragDrop() {
            console.log('Testing drag drop...');
            
            try {
                const question = {
                    type: 'drag-drop',
                    dragElements: [
                        { value: '7', type: 'number', display: '7' },
                        { value: '3', type: 'number', display: '3' }
                    ],
                    dropZones: [
                        { id: 'blank1', placeholder: 'Drop number here' }
                    ]
                };
                
                if (window.mathInteractiveComponents) {
                    window.mathInteractiveComponents.showDragDropPuzzle(question);
                    showStatus('drag-drop-status', 'Drag & drop component loaded successfully!');
                } else {
                    showStatus('drag-drop-status', 'mathInteractiveComponents not found!', false);
                }
            } catch (error) {
                console.error('Drag drop test error:', error);
                showStatus('drag-drop-status', `Error: ${error.message}`, false);
            }
        }
        
        function testVisualManipulatives() {
            console.log('Testing visual manipulatives...');
            
            try {
                const question = {
                    type: 'visual-manipulatives',
                    manipulativeType: 'fractions',
                    fractions: [
                        { numerator: 1, denominator: 2 }
                    ]
                };
                
                if (window.mathInteractiveComponents) {
                    window.mathInteractiveComponents.showVisualManipulatives(question);
                    showStatus('manipulatives-status', 'Visual manipulatives component loaded successfully!');
                } else {
                    showStatus('manipulatives-status', 'mathInteractiveComponents not found!', false);
                }
            } catch (error) {
                console.error('Visual manipulatives test error:', error);
                showStatus('manipulatives-status', `Error: ${error.message}`, false);
            }
        }
        
        function checkComponents() {
            console.log('Checking component status...');
            
            const checks = {
                'mathInteractiveComponents': !!window.mathInteractiveComponents,
                'MathQuill': typeof MQ !== 'undefined',
                'jQuery': typeof $ !== 'undefined'
            };
            
            const results = Object.entries(checks).map(([name, status]) => 
                `${name}: ${status ? '✓' : '✗'}`
            ).join('\n');
            
            const allGood = Object.values(checks).every(status => status);
            
            showStatus('component-status', results, allGood);
            
            console.log('Component check results:', checks);
        }
        
        // Auto-check components on load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Page loaded, checking components...');
            setTimeout(checkComponents, 1000);
        });
    </script>
</body>
</html>
