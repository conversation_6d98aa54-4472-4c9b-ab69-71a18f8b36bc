/**
 * Interactive Mathematics Assessment Components
 * Provides enhanced interactive question types for the mathematics assessment system
 */

class MathInteractiveComponents {
  constructor() {
    this.mathQuill = null;
    this.equationEditor = null;
    this.currentNumberLineValue = 0;
    this.currentStep = 0;
    this.totalSteps = 0;
    this.coordinatePoints = [];
    this.manipulativesState = {};
    this.selectedDragElement = null;

    // Initialize MathQuill when available
    this.initializeMathQuill();
  }

  /**
   * Initialize MathQuill library
   */
  initializeMathQuill() {
    if (typeof MQ !== 'undefined') {
      this.mathQuill = MQ.MathField;
      console.log('MathQuill initialized successfully');
    } else {
      // Retry after a short delay if MathQuill isn't loaded yet
      setTimeout(() => this.initializeMathQuill(), 100);
    }
  }

  /**
   * Show number line slider for positioning and ordering questions
   */
  showNumberLine(question) {
    const container = document.getElementById('number-line-input');
    const widget = document.getElementById('number-line-widget');
    
    // Configure number line based on question parameters
    const min = question.numberLine?.min || 0;
    const max = question.numberLine?.max || 10;
    const step = question.numberLine?.step || 1;
    
    this.setupNumberLine(widget, min, max, step);
    container.classList.remove('hidden');
    
    // Enable next button when value is set
    this.enableNextButtonOnInteraction();
  }

  /**
   * Setup interactive number line
   */
  setupNumberLine(widget, min, max, step) {
    const track = widget.querySelector('.number-line-track');
    const markersContainer = widget.querySelector('.number-line-markers');
    const slider = widget.querySelector('.number-line-slider');
    const valueDisplay = widget.querySelector('.number-line-value-display');

    // Add accessibility attributes
    slider.setAttribute('role', 'slider');
    slider.setAttribute('aria-label', `Number line slider from ${min} to ${max}`);
    slider.setAttribute('aria-valuemin', min);
    slider.setAttribute('aria-valuemax', max);
    slider.setAttribute('aria-valuenow', min + (max - min) / 2);
    slider.setAttribute('tabindex', '0');

    // Clear existing content
    markersContainer.innerHTML = '';

    // Create markers
    const range = max - min;
    const markerCount = Math.floor(range / step) + 1;

    for (let i = 0; i < markerCount; i++) {
      const value = min + (i * step);
      const position = (i / (markerCount - 1)) * 100;

      const marker = document.createElement('div');
      marker.className = 'number-line-marker';
      marker.style.left = `${position}%`;
      marker.innerHTML = `<span class="marker-value">${value}</span>`;
      marker.setAttribute('aria-label', `Marker at ${value}`);
      markersContainer.appendChild(marker);
    }
    
    // Setup slider interaction
    let isDragging = false;
    
    slider.addEventListener('mousedown', (e) => {
      isDragging = true;
      e.preventDefault();
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      
      const rect = track.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      
      slider.style.left = `${percentage}%`;
      
      // Calculate value
      const value = min + (percentage / 100) * range;
      const snappedValue = Math.round(value / step) * step;
      this.currentNumberLineValue = snappedValue;
      
      valueDisplay.textContent = snappedValue;
      valueDisplay.style.left = `${percentage}%`;
    });
    
    document.addEventListener('mouseup', () => {
      isDragging = false;
    });

    // Add keyboard navigation
    slider.addEventListener('keydown', (e) => {
      let newValue = this.currentNumberLineValue;

      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowDown':
          newValue = Math.max(min, newValue - step);
          e.preventDefault();
          break;
        case 'ArrowRight':
        case 'ArrowUp':
          newValue = Math.min(max, newValue + step);
          e.preventDefault();
          break;
        case 'Home':
          newValue = min;
          e.preventDefault();
          break;
        case 'End':
          newValue = max;
          e.preventDefault();
          break;
      }

      if (newValue !== this.currentNumberLineValue) {
        this.currentNumberLineValue = newValue;
        const percentage = ((newValue - min) / range) * 100;
        slider.style.left = `${percentage}%`;
        valueDisplay.textContent = newValue;
        valueDisplay.style.left = `${percentage}%`;
        slider.setAttribute('aria-valuenow', newValue);
      }
    });

    // Initialize at middle position
    const initialPosition = 50;
    slider.style.left = `${initialPosition}%`;
    this.currentNumberLineValue = min + (range / 2);
    valueDisplay.textContent = this.currentNumberLineValue;
    valueDisplay.style.left = `${initialPosition}%`;
    slider.setAttribute('aria-valuenow', this.currentNumberLineValue);
  }

  /**
   * Show drag-and-drop puzzle interface
   */
  showDragDropPuzzle(question) {
    const container = document.getElementById('drag-drop-input');
    const elementsBank = document.getElementById('drag-elements-bank');
    const dropZones = document.getElementById('drop-zones-container');
    
    // Clear existing content
    elementsBank.innerHTML = '';
    dropZones.innerHTML = '';
    
    // Create draggable elements
    question.dragElements?.forEach((element, index) => {
      const dragElement = document.createElement('div');
      dragElement.className = 'drag-element';
      dragElement.draggable = true;
      dragElement.dataset.value = element.value;
      dragElement.dataset.type = element.type;
      dragElement.innerHTML = element.display;

      // Add accessibility attributes
      dragElement.setAttribute('role', 'button');
      dragElement.setAttribute('aria-label', `Draggable element: ${element.display}`);
      dragElement.setAttribute('tabindex', '0');

      dragElement.addEventListener('dragstart', (e) => {
        e.dataTransfer.setData('text/plain', element.value);
        e.dataTransfer.setData('application/json', JSON.stringify(element));
      });

      // Add keyboard support for drag elements
      dragElement.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // Simulate drag start for keyboard users
          this.selectedDragElement = element;
          dragElement.classList.add('keyboard-selected');

          // Announce to screen readers
          const announcement = document.createElement('div');
          announcement.setAttribute('aria-live', 'polite');
          announcement.textContent = `Selected ${element.display}. Use arrow keys to navigate to a drop zone and press Enter to drop.`;
          announcement.style.position = 'absolute';
          announcement.style.left = '-9999px';
          document.body.appendChild(announcement);
          setTimeout(() => document.body.removeChild(announcement), 1000);
        }
      });

      elementsBank.appendChild(dragElement);
    });
    
    // Create drop zones
    question.dropZones?.forEach((zone, index) => {
      const dropZone = document.createElement('div');
      dropZone.className = 'drop-zone';
      dropZone.dataset.zoneId = zone.id;
      dropZone.innerHTML = `<span class="drop-placeholder">${zone.placeholder}</span>`;

      // Add accessibility attributes
      dropZone.setAttribute('role', 'button');
      dropZone.setAttribute('aria-label', `Drop zone: ${zone.placeholder}`);
      dropZone.setAttribute('tabindex', '0');
      dropZone.setAttribute('aria-dropeffect', 'move');

      // Add keyboard support for drop zones
      dropZone.addEventListener('keydown', (e) => {
        if ((e.key === 'Enter' || e.key === ' ') && this.selectedDragElement) {
          e.preventDefault();

          // Simulate drop for keyboard users
          dropZone.innerHTML = `<span class="dropped-element">${this.selectedDragElement.display}</span>`;
          dropZone.dataset.value = this.selectedDragElement.value;

          // Remove keyboard selection
          const selectedElement = document.querySelector('.drag-element.keyboard-selected');
          if (selectedElement) {
            selectedElement.classList.remove('keyboard-selected');
            selectedElement.style.display = 'none';
          }

          this.selectedDragElement = null;
          this.checkDragDropCompletion();

          // Announce to screen readers
          const announcement = document.createElement('div');
          announcement.setAttribute('aria-live', 'polite');
          announcement.textContent = `Dropped ${this.selectedDragElement?.display || 'element'} in ${zone.placeholder}`;
          announcement.style.position = 'absolute';
          announcement.style.left = '-9999px';
          document.body.appendChild(announcement);
          setTimeout(() => document.body.removeChild(announcement), 1000);
        }
      });

      this.setupDropZone(dropZone);
      dropZones.appendChild(dropZone);
    });
    
    container.classList.remove('hidden');
    this.enableNextButtonOnInteraction();
  }

  /**
   * Setup drop zone functionality
   */
  setupDropZone(dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', () => {
      dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('drag-over');
      
      const value = e.dataTransfer.getData('text/plain');
      const elementData = JSON.parse(e.dataTransfer.getData('application/json'));
      
      // Update drop zone content
      dropZone.innerHTML = `<span class="dropped-element">${elementData.display}</span>`;
      dropZone.dataset.value = value;
      
      // Check if all zones are filled
      this.checkDragDropCompletion();
    });
  }

  /**
   * Check if drag-and-drop puzzle is complete
   */
  checkDragDropCompletion() {
    const dropZones = document.querySelectorAll('#drop-zones-container .drop-zone');
    const filledZones = Array.from(dropZones).filter(zone => zone.dataset.value);
    
    if (filledZones.length === dropZones.length) {
      document.getElementById('next-question-btn').disabled = false;
    }
  }

  /**
   * Show equation builder with MathQuill
   */
  showEquationBuilder(question) {
    const container = document.getElementById('equation-builder-input');
    const editorElement = document.getElementById('equation-editor');
    
    if (!this.mathQuill) {
      console.error('MathQuill not available');
      return;
    }
    
    // Add accessibility attributes to editor
    editorElement.setAttribute('role', 'textbox');
    editorElement.setAttribute('aria-label', 'Mathematical equation editor');
    editorElement.setAttribute('aria-multiline', 'false');

    // Initialize MathQuill editor
    this.equationEditor = this.mathQuill(editorElement, {
      spaceBehavesLikeTab: true,
      leftRightIntoCmdGoes: 'up',
      restrictMismatchedBrackets: true,
      sumStartsWithNEquals: true,
      supSubsRequireOperand: true,
      charsThatBreakOutOfSupSub: '+-=<>',
      autoSubscriptNumerals: true,
      autoCommands: 'pi theta sqrt sum prod alpha beta gamma delta epsilon zeta eta mu nu xi rho sigma tau phi chi psi omega',
      autoOperatorNames: 'sin cos tan sec csc cot sinh cosh tanh log ln exp lim max min',
      handlers: {
        edit: () => {
          // Enable next button when equation is entered
          const latex = this.equationEditor.latex();
          document.getElementById('next-question-btn').disabled = latex.trim() === '';

          // Update aria-label with current equation
          const readableEquation = this.latexToReadable(latex);
          editorElement.setAttribute('aria-label', `Mathematical equation editor. Current equation: ${readableEquation || 'empty'}`);
        }
      }
    });
    
    // Setup toolbar buttons
    this.setupEquationToolbar();
    
    container.classList.remove('hidden');
    editorElement.focus();
  }

  /**
   * Setup equation builder toolbar
   */
  setupEquationToolbar() {
    const toolbar = document.getElementById('equation-toolbar');
    const buttons = toolbar.querySelectorAll('.toolbar-btn');

    // Add toolbar accessibility
    toolbar.setAttribute('role', 'toolbar');
    toolbar.setAttribute('aria-label', 'Mathematical symbols toolbar');

    buttons.forEach((button, index) => {
      const symbol = button.dataset.symbol;
      const symbolName = this.getSymbolName(symbol);

      // Add accessibility attributes
      button.setAttribute('role', 'button');
      button.setAttribute('aria-label', `Insert ${symbolName}`);
      button.setAttribute('tabindex', index === 0 ? '0' : '-1');

      button.addEventListener('click', () => {
        this.insertSymbol(symbol);
      });

      // Add keyboard navigation for toolbar
      button.addEventListener('keydown', (e) => {
        let targetIndex = index;

        switch (e.key) {
          case 'ArrowLeft':
            targetIndex = index > 0 ? index - 1 : buttons.length - 1;
            e.preventDefault();
            break;
          case 'ArrowRight':
            targetIndex = index < buttons.length - 1 ? index + 1 : 0;
            e.preventDefault();
            break;
          case 'Enter':
          case ' ':
            this.insertSymbol(symbol);
            e.preventDefault();
            break;
        }

        if (targetIndex !== index) {
          buttons[index].setAttribute('tabindex', '-1');
          buttons[targetIndex].setAttribute('tabindex', '0');
          buttons[targetIndex].focus();
        }
      });
    });
  }

  /**
   * Get readable name for mathematical symbol
   */
  getSymbolName(symbol) {
    const symbolNames = {
      '+': 'plus',
      '-': 'minus',
      '*': 'multiply',
      '/': 'divide',
      '^': 'exponent',
      'sqrt': 'square root',
      'frac': 'fraction',
      '(': 'left parenthesis',
      ')': 'right parenthesis'
    };

    return symbolNames[symbol] || symbol;
  }

  /**
   * Convert LaTeX to readable text for screen readers
   */
  latexToReadable(latex) {
    if (!latex) return '';

    return latex
      .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '$1 over $2')
      .replace(/\\sqrt\{([^}]+)\}/g, 'square root of $1')
      .replace(/\^(\w+)/g, ' to the power of $1')
      .replace(/\\cdot/g, ' times ')
      .replace(/\{|\}/g, '')
      .trim();
  }

  /**
   * Insert symbol into equation editor
   */
  insertSymbol(symbol) {
    if (!this.equationEditor) return;
    
    const symbolMap = {
      '+': '+',
      '-': '-',
      '*': '\\cdot',
      '/': '\\frac{}{}',
      '^': '^{}',
      'sqrt': '\\sqrt{}',
      'frac': '\\frac{}{}',
      '(': '(',
      ')': ')'
    };
    
    const latex = symbolMap[symbol] || symbol;
    this.equationEditor.write(latex);
    this.equationEditor.focus();
  }

  /**
   * Enable next button when user interacts with components
   */
  enableNextButtonOnInteraction() {
    // This will be called by individual components when they detect user interaction
    setTimeout(() => {
      document.getElementById('next-question-btn').disabled = false;
    }, 500);
  }

  /**
   * Get current answer based on active question type
   */
  getCurrentAnswer(questionType) {
    switch (questionType) {
      case 'number-line':
        return this.currentNumberLineValue;
      
      case 'drag-drop':
        return this.getDragDropAnswer();
      
      case 'equation-builder':
        return this.equationEditor ? this.equationEditor.latex() : '';
      
      case 'step-by-step':
        return this.getStepByStepAnswer();
      
      case 'coordinate-grid':
        return this.coordinatePoints;
      
      case 'visual-manipulatives':
        return this.manipulativesState;
      
      default:
        return '';
    }
  }

  /**
   * Get drag-and-drop answer
   */
  getDragDropAnswer() {
    const dropZones = document.querySelectorAll('#drop-zones-container .drop-zone');
    const answer = {};

    dropZones.forEach(zone => {
      answer[zone.dataset.zoneId] = zone.dataset.value || '';
    });

    return answer;
  }

  /**
   * Show step-by-step worked answer interface
   */
  showStepByStep(question) {
    const container = document.getElementById('step-by-step-input');
    const stepsContainer = document.getElementById('steps-container');
    const stepIndicator = document.getElementById('step-indicator');

    this.totalSteps = question.steps?.length || 0;
    this.currentStep = 0;

    // Clear existing content
    stepsContainer.innerHTML = '';

    // Create step elements
    question.steps?.forEach((step, index) => {
      const stepElement = document.createElement('div');
      stepElement.className = `step-element ${index === 0 ? 'active' : 'hidden'}`;
      stepElement.dataset.stepIndex = index;

      stepElement.innerHTML = `
        <div class="step-header">
          <h4>Step ${index + 1}: ${step.title}</h4>
        </div>
        <div class="step-content">
          <p class="step-instruction">${step.instruction}</p>
          <div class="step-input-area">
            ${this.createStepInput(step)}
          </div>
        </div>
      `;

      stepsContainer.appendChild(stepElement);
    });

    // Update step indicator
    stepIndicator.textContent = `Step 1 of ${this.totalSteps}`;

    // Setup navigation
    this.setupStepNavigation();

    container.classList.remove('hidden');
  }

  /**
   * Create input for individual step
   */
  createStepInput(step) {
    switch (step.inputType) {
      case 'numeric':
        return `<input type="text" class="step-input numeric" placeholder="${step.placeholder || 'Enter your answer'}" data-step-input>`;

      case 'equation':
        return `<div class="step-equation-editor" data-step-input></div>`;

      case 'multiple-choice':
        return step.options.map((option, index) =>
          `<label class="step-option">
            <input type="radio" name="step-${step.index}" value="${option.value}" data-step-input>
            <span>${option.display}</span>
          </label>`
        ).join('');

      default:
        return `<input type="text" class="step-input" placeholder="Enter your answer" data-step-input>`;
    }
  }

  /**
   * Setup step navigation
   */
  setupStepNavigation() {
    const prevBtn = document.getElementById('prev-step-btn');
    const nextBtn = document.getElementById('next-step-btn');
    const stepIndicator = document.getElementById('step-indicator');

    prevBtn.addEventListener('click', () => {
      if (this.currentStep > 0) {
        this.showStep(this.currentStep - 1);
      }
    });

    nextBtn.addEventListener('click', () => {
      if (this.currentStep < this.totalSteps - 1) {
        this.showStep(this.currentStep + 1);
      } else {
        // Last step completed
        document.getElementById('next-question-btn').disabled = false;
      }
    });

    // Monitor input changes to enable navigation
    document.addEventListener('input', (e) => {
      if (e.target.hasAttribute('data-step-input')) {
        this.validateCurrentStep();
      }
    });

    document.addEventListener('change', (e) => {
      if (e.target.hasAttribute('data-step-input')) {
        this.validateCurrentStep();
      }
    });
  }

  /**
   * Show specific step
   */
  showStep(stepIndex) {
    const steps = document.querySelectorAll('.step-element');
    const stepIndicator = document.getElementById('step-indicator');
    const prevBtn = document.getElementById('prev-step-btn');
    const nextBtn = document.getElementById('next-step-btn');

    // Hide all steps
    steps.forEach(step => step.classList.add('hidden'));

    // Show current step
    if (steps[stepIndex]) {
      steps[stepIndex].classList.remove('hidden');
      steps[stepIndex].classList.add('active');
    }

    this.currentStep = stepIndex;

    // Update navigation
    prevBtn.disabled = stepIndex === 0;
    nextBtn.textContent = stepIndex === this.totalSteps - 1 ? 'Complete' : 'Next Step';
    stepIndicator.textContent = `Step ${stepIndex + 1} of ${this.totalSteps}`;

    // Validate current step
    this.validateCurrentStep();
  }

  /**
   * Validate current step input
   */
  validateCurrentStep() {
    const currentStepElement = document.querySelector('.step-element.active');
    if (!currentStepElement) return;

    const inputs = currentStepElement.querySelectorAll('[data-step-input]');
    let isValid = false;

    inputs.forEach(input => {
      if (input.type === 'radio') {
        const radioGroup = currentStepElement.querySelectorAll(`input[name="${input.name}"]`);
        isValid = Array.from(radioGroup).some(radio => radio.checked);
      } else {
        isValid = input.value.trim() !== '';
      }
    });

    document.getElementById('next-step-btn').disabled = !isValid;
  }

  /**
   * Get step-by-step answer
   */
  getStepByStepAnswer() {
    const steps = document.querySelectorAll('.step-element');
    const answers = {};

    steps.forEach((step, index) => {
      const inputs = step.querySelectorAll('[data-step-input]');
      const stepAnswers = [];

      inputs.forEach(input => {
        if (input.type === 'radio' && input.checked) {
          stepAnswers.push(input.value);
        } else if (input.type !== 'radio' && input.value.trim()) {
          stepAnswers.push(input.value.trim());
        }
      });

      answers[`step_${index + 1}`] = stepAnswers;
    });

    return answers;
  }

  /**
   * Show coordinate grid interface
   */
  showCoordinateGrid(question) {
    const container = document.getElementById('coordinate-grid-input');
    const svg = document.getElementById('grid-svg');

    // Clear existing content
    svg.innerHTML = '';
    this.coordinatePoints = [];

    // Setup grid
    this.setupCoordinateGrid(svg, question.gridConfig || {});

    // Setup tools
    this.setupGridTools();

    container.classList.remove('hidden');
    this.enableNextButtonOnInteraction();
  }

  /**
   * Setup coordinate grid
   */
  setupCoordinateGrid(svg, config) {
    const width = 400;
    const height = 400;
    const gridSize = config.gridSize || 20;
    const centerX = width / 2;
    const centerY = height / 2;

    // Add accessibility attributes to SVG
    svg.setAttribute('role', 'img');
    svg.setAttribute('aria-label', 'Interactive coordinate grid for plotting points');
    svg.setAttribute('tabindex', '0');

    // Create grid lines
    for (let i = 0; i <= width; i += gridSize) {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', i);
      line.setAttribute('y1', 0);
      line.setAttribute('x2', i);
      line.setAttribute('y2', height);
      line.setAttribute('stroke', '#e5e7eb');
      line.setAttribute('stroke-width', i === centerX ? '2' : '1');
      svg.appendChild(line);
    }

    for (let i = 0; i <= height; i += gridSize) {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', 0);
      line.setAttribute('y1', i);
      line.setAttribute('x2', width);
      line.setAttribute('y2', i);
      line.setAttribute('stroke', '#e5e7eb');
      line.setAttribute('stroke-width', i === centerY ? '2' : '1');
      svg.appendChild(line);
    }

    // Add axis labels
    this.addAxisLabels(svg, gridSize, centerX, centerY);

    // Setup click handler for plotting points
    svg.addEventListener('click', (e) => {
      if (this.currentGridTool === 'point') {
        this.plotPoint(e, svg, gridSize, centerX, centerY);
      }
    });

    // Add keyboard navigation for coordinate grid
    let currentGridX = 0;
    let currentGridY = 0;

    svg.addEventListener('keydown', (e) => {
      const maxGridX = Math.floor(centerX / gridSize);
      const maxGridY = Math.floor(centerY / gridSize);

      switch (e.key) {
        case 'ArrowLeft':
          currentGridX = Math.max(-maxGridX, currentGridX - 1);
          e.preventDefault();
          break;
        case 'ArrowRight':
          currentGridX = Math.min(maxGridX, currentGridX + 1);
          e.preventDefault();
          break;
        case 'ArrowUp':
          currentGridY = Math.min(maxGridY, currentGridY + 1);
          e.preventDefault();
          break;
        case 'ArrowDown':
          currentGridY = Math.max(-maxGridY, currentGridY - 1);
          e.preventDefault();
          break;
        case 'Enter':
        case ' ':
          if (this.currentGridTool === 'point') {
            // Plot point at current keyboard position
            const snapX = centerX + currentGridX * gridSize;
            const snapY = centerY - currentGridY * gridSize;

            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', snapX);
            circle.setAttribute('cy', snapY);
            circle.setAttribute('r', '4');
            circle.setAttribute('fill', '#3b82f6');
            circle.setAttribute('stroke', '#1d4ed8');
            circle.setAttribute('stroke-width', '2');
            circle.classList.add('plotted-point');
            circle.setAttribute('aria-label', `Point at coordinates (${currentGridX}, ${currentGridY})`);

            svg.appendChild(circle);
            this.coordinatePoints.push({ x: currentGridX, y: currentGridY });
            document.getElementById('next-question-btn').disabled = false;

            // Announce to screen readers
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.textContent = `Point plotted at coordinates (${currentGridX}, ${currentGridY})`;
            announcement.style.position = 'absolute';
            announcement.style.left = '-9999px';
            document.body.appendChild(announcement);
            setTimeout(() => document.body.removeChild(announcement), 1000);
          }
          e.preventDefault();
          break;
      }

      // Update aria-label with current position
      svg.setAttribute('aria-label', `Interactive coordinate grid. Current position: (${currentGridX}, ${currentGridY}). Press Enter to plot point.`);
    });
  }

  /**
   * Add axis labels to coordinate grid
   */
  addAxisLabels(svg, gridSize, centerX, centerY) {
    const maxX = Math.floor(centerX / gridSize);
    const maxY = Math.floor(centerY / gridSize);

    // X-axis labels
    for (let i = -maxX; i <= maxX; i++) {
      if (i === 0) continue;
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', centerX + i * gridSize);
      text.setAttribute('y', centerY + 15);
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '12');
      text.setAttribute('fill', '#6b7280');
      text.textContent = i;
      svg.appendChild(text);
    }

    // Y-axis labels
    for (let i = -maxY; i <= maxY; i++) {
      if (i === 0) continue;
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', centerX - 15);
      text.setAttribute('y', centerY - i * gridSize + 5);
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '12');
      text.setAttribute('fill', '#6b7280');
      text.textContent = i;
      svg.appendChild(text);
    }
  }

  /**
   * Setup grid tools
   */
  setupGridTools() {
    const tools = document.querySelectorAll('.grid-tool-btn');
    this.currentGridTool = 'point';

    tools.forEach(tool => {
      tool.addEventListener('click', () => {
        // Remove active class from all tools
        tools.forEach(t => t.classList.remove('active'));

        // Add active class to clicked tool
        tool.classList.add('active');

        // Set current tool
        this.currentGridTool = tool.dataset.tool;

        // Handle special tools
        if (this.currentGridTool === 'clear') {
          this.clearGrid();
        }
      });
    });
  }

  /**
   * Plot point on coordinate grid
   */
  plotPoint(e, svg, gridSize, centerX, centerY) {
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Snap to grid
    const gridX = Math.round((x - centerX) / gridSize);
    const gridY = Math.round((centerY - y) / gridSize);
    const snapX = centerX + gridX * gridSize;
    const snapY = centerY - gridY * gridSize;

    // Create point
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    circle.setAttribute('cx', snapX);
    circle.setAttribute('cy', snapY);
    circle.setAttribute('r', '4');
    circle.setAttribute('fill', '#3b82f6');
    circle.setAttribute('stroke', '#1d4ed8');
    circle.setAttribute('stroke-width', '2');
    circle.classList.add('plotted-point');

    svg.appendChild(circle);

    // Store point data
    this.coordinatePoints.push({ x: gridX, y: gridY });

    // Enable next button
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Clear coordinate grid
   */
  clearGrid() {
    const svg = document.getElementById('grid-svg');
    const points = svg.querySelectorAll('.plotted-point');
    points.forEach(point => point.remove());

    this.coordinatePoints = [];
    document.getElementById('next-question-btn').disabled = true;
  }

  /**
   * Show visual manipulatives interface
   */
  showVisualManipulatives(question) {
    const container = document.getElementById('visual-manipulatives-input');
    const workspace = document.getElementById('manipulatives-workspace');
    const toolbox = document.getElementById('manipulatives-toolbox');

    // Clear existing content
    workspace.innerHTML = '';
    toolbox.innerHTML = '';
    this.manipulativesState = {};

    // Setup based on question type
    this.setupManipulatives(question, workspace, toolbox);

    container.classList.remove('hidden');
    this.enableNextButtonOnInteraction();
  }

  /**
   * Setup visual manipulatives based on question type
   */
  setupManipulatives(question, workspace, toolbox) {
    const type = question.manipulativeType || 'fractions';

    switch (type) {
      case 'fractions':
        this.setupFractionBars(workspace, toolbox, question);
        break;

      case 'geometry':
        this.setupGeometricShapes(workspace, toolbox, question);
        break;

      case 'counting':
        this.setupCountingBlocks(workspace, toolbox, question);
        break;

      default:
        this.setupGenericManipulatives(workspace, toolbox, question);
    }
  }

  /**
   * Setup fraction bars manipulative
   */
  setupFractionBars(workspace, toolbox, question) {
    const fractions = question.fractions || [
      { numerator: 1, denominator: 2 },
      { numerator: 1, denominator: 3 },
      { numerator: 1, denominator: 4 }
    ];

    fractions.forEach((fraction, index) => {
      const fractionBar = document.createElement('div');
      fractionBar.className = 'fraction-bar';
      fractionBar.dataset.fraction = `${fraction.numerator}/${fraction.denominator}`;

      // Add accessibility attributes
      fractionBar.setAttribute('role', 'group');
      fractionBar.setAttribute('aria-label', `Fraction bar for ${fraction.numerator}/${fraction.denominator}`);

      // Create segments
      for (let i = 0; i < fraction.denominator; i++) {
        const segment = document.createElement('div');
        segment.className = 'fraction-segment';
        segment.dataset.segmentIndex = i;

        // Add accessibility attributes
        segment.setAttribute('role', 'button');
        segment.setAttribute('aria-label', `Segment ${i + 1} of ${fraction.denominator}`);
        segment.setAttribute('tabindex', '0');

        if (i < fraction.numerator) {
          segment.classList.add('filled');
          segment.setAttribute('aria-pressed', 'true');
        } else {
          segment.setAttribute('aria-pressed', 'false');
        }

        const toggleSegment = () => {
          segment.classList.toggle('filled');
          segment.setAttribute('aria-pressed', segment.classList.contains('filled'));
          this.updateManipulativesState();
        };

        segment.addEventListener('click', toggleSegment);

        // Add keyboard support
        segment.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleSegment();
          }
        });

        fractionBar.appendChild(segment);
      }

      toolbox.appendChild(fractionBar);
    });
  }

  /**
   * Update manipulatives state
   */
  updateManipulativesState() {
    // Collect current state of all manipulatives
    const fractionBars = document.querySelectorAll('.fraction-bar');

    fractionBars.forEach((bar, index) => {
      const segments = bar.querySelectorAll('.fraction-segment');
      const filledSegments = bar.querySelectorAll('.fraction-segment.filled');

      this.manipulativesState[`fraction_${index}`] = {
        total: segments.length,
        filled: filledSegments.length,
        fraction: bar.dataset.fraction
      };
    });

    // Enable next button if any interaction occurred
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Reset all interactive components
   */
  reset() {
    this.currentNumberLineValue = 0;
    this.currentStep = 0;
    this.totalSteps = 0;
    this.coordinatePoints = [];
    this.manipulativesState = {};
    this.currentGridTool = 'point';
    this.selectedDragElement = null;

    // Clear keyboard selections
    const keyboardSelected = document.querySelectorAll('.keyboard-selected');
    keyboardSelected.forEach(element => element.classList.remove('keyboard-selected'));

    if (this.equationEditor) {
      this.equationEditor.latex('');
    }
  }

  /**
   * Announce message to screen readers
   */
  announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.textContent = message;
    announcement.style.position = 'absolute';
    announcement.style.left = '-9999px';
    announcement.style.width = '1px';
    announcement.style.height = '1px';
    announcement.style.overflow = 'hidden';

    document.body.appendChild(announcement);
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }
}

// Initialize global instance
window.mathInteractiveComponents = new MathInteractiveComponents();
