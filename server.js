// Import required modules
require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const sgMail = require('@sendgrid/mail');
const OpenAI = require('openai');
const admin = require('firebase-admin');
const cors = require('cors');
const axios = require('axios');
const NodeCache = require('node-cache');
const roleCache = new NodeCache({ stdTTL: 3600 });
const requestCache = new NodeCache({ stdTTL: 3600 }); // Cache for 1 hour

// Active requests tracking to prevent race conditions
const activeRequests = new Set();

// Helper function to check for duplicate requests within a session
function isDuplicateRequest(email, endpoint, params) {
  if (!email) return false;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;

  // Check both the cache and active requests
  if (requestCache.has(cacheKey) || activeRequests.has(cacheKey)) {
    console.log(`Duplicate request detected: ${cacheKey}`);
    return true;
  }

  // Mark as active immediately to prevent race conditions
  activeRequests.add(cacheKey);
  return false;
}

// Helper function to track processed requests
function trackRequest(email, endpoint, params) {
  if (!email) return;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;
  requestCache.set(cacheKey, true);

  // Remove from active requests
  activeRequests.delete(cacheKey);

  console.log(`Request tracked: ${cacheKey}`);
}

// Helper function to release a request if it fails
function releaseRequest(email, endpoint, params) {
  if (!email) return;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;
  activeRequests.delete(cacheKey);
  console.log(`Request released: ${cacheKey}`);
}

// Function to extract courses from learning path JSON
function extractLearningPathData() {
  try {
    // Require the learning path data file
    const learningPathData = require('./learning-path-data.json');

    // Initialize an object to store all course information
    const courseData = {
      pathways: [],
      allCourses: [],
      coursesByPathway: {},
      coursesByCategory: {}
    };

    // Process each learning pathway
    for (const pathway of ['essentials', 'intermediate', 'advanced', 'champions']) {
      if (!learningPathData[pathway]) continue;

      const pathwayTitle = learningPathData[pathway].title;
      courseData.pathways.push(pathwayTitle);
      courseData.coursesByPathway[pathwayTitle] = [];

      // Process each category in the pathway
      learningPathData[pathway].courseCategories.forEach(category => {
        const categoryName = category.category;

        if (!courseData.coursesByCategory[categoryName]) {
          courseData.coursesByCategory[categoryName] = [];
        }

        // Process each course in the category
        category.courses.forEach(course => {
          const courseInfo = {
            title: course.title,
            description: course.description,
            pathway: pathwayTitle,
            category: categoryName,
            level: course.level
          };

          // Add to all relevant collections
          courseData.allCourses.push(courseInfo);
          courseData.coursesByPathway[pathwayTitle].push(courseInfo);
          courseData.coursesByCategory[categoryName].push(courseInfo);
        });
      });
    }

    // Calculate progression chains
    courseData.progressionChains = calculateProgressionChains(courseData.allCourses);

    return courseData;
  } catch (error) {
    console.error('Error extracting learning path data:', error);
    return null;
  }
}

// Helper function to identify course progression chains
function calculateProgressionChains(allCourses) {
  const chains = {};

  // Group courses by base name (without level)
  const coursesByBase = {};
  allCourses.forEach(course => {
    const baseName = course.title.split(' - ')[0];
    if (!coursesByBase[baseName]) {
      coursesByBase[baseName] = [];
    }
    coursesByBase[baseName].push(course);
  });

  // For each base course type, create progression chains
  Object.keys(coursesByBase).forEach(baseName => {
    const courses = coursesByBase[baseName];

    // Sort by pathway level (Essentials -> Intermediate -> Advanced -> Champions)
    const sortedCourses = courses.sort((a, b) => {
      const pathwayOrder = {
        'Essentials Learning Pathway': 1,
        'Intermediate Learning Pathway': 2,
        'Advanced Learning Pathway': 3,
        'Champions Learning Pathway': 4
      };
      return pathwayOrder[a.pathway] - pathwayOrder[b.pathway];
    });

    // Create chains where each course points to courses at the next level
    sortedCourses.forEach((course, index) => {
      if (index < sortedCourses.length - 1) {
        chains[course.title] = sortedCourses.slice(index + 1).map(c => c.title);
      } else {
        chains[course.title] = [];
      }
    });
  });

  return chains;
}

// Initialize Express app
const app = express();
const port = process.env.PORT || 3000;

// Global variable to store course data
let globalLearningPathData;

// Initialize learning path data at startup
async function initializeLearningPathData() {
  console.log('Initializing learning path data...');
  globalLearningPathData = extractLearningPathData();

  if (globalLearningPathData) {
    console.log('Learning path data loaded successfully');
    console.log(`Loaded ${globalLearningPathData.allCourses.length} courses across ${globalLearningPathData.pathways.length} pathways`);
  } else {
    console.error('Failed to load learning path data');
  }
}

// Initialize Birmingham company for student-focused version
async function initializeBirminghamCompany() {
  try {
    console.log('Checking Birmingham company setup...');
    const companyDoc = await firestore
      .collection('companies')
      .doc('Birmingham')
      .get();

    if (!companyDoc.exists) {
      console.log('Creating Birmingham company for student users...');
      await firestore
        .collection('companies')
        .doc('Birmingham')
        .set({
          name: 'Birmingham',
          type: 'student-focused',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          description: 'Auto-created company for student assessments'
        });
      console.log('Birmingham company created successfully');
    } else {
      console.log('Birmingham company already exists');
    }
  } catch (error) {
    console.error('Error initializing Birmingham company:', error);
  }
}

// Configure middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Import your Firebase service account key JSON file
const serviceAccount = require('./service_account.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/"
});

// Initialize Firebase services
const db = admin.database();
const firestore = admin.firestore();
const processedResultsRef = db.ref('processedResults');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Function to send email using SendGrid
const sendMail = async (to, templateId, dynamicData) => {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  const msg = {
    to,
    from: {
      name: 'Barefoot eLearning',
      email: process.env.FROM_EMAIL
    },
    templateId,
    dynamicTemplateData: dynamicData,
    isMultiple: true,
    use_template_subject: true
  };
  try {
    await sgMail.send(msg);
    console.log('Email sent');
  } catch (error) {
    console.error(error);
    if (error.response) {
      console.error(error.response.body)
    }
  }
}

// Route handler for the root path
app.get('/', (_, res) => {
  res.send('Hello, World!');
});

// Route to receive the key from the client
app.post('/receive-key', async (req, res) => {
  const key = req.body.key;
  console.log('Received key from client:', key);

  if (!key) {
    console.error('No key provided in the request body');
    return res.status(400).json({ error: 'Invalid request: No key provided' });
  }

  try {
    const userRef = db.ref('users').child(key);
    const userSnapshot = await userRef.once('value');
    const userData = userSnapshot.val();

    if (userData) {
      const { firstName, 'lite assesment score': score, userEmail } = userData;
      const totalQuestions = 10;
      const scorePercentage = (score / totalQuestions) * 100;

      let remarks;
      if (scorePercentage < 30) {
        remarks = "Low knowledge";
      } else if (scorePercentage < 50) {
        remarks = "Fair knowledge";
      } else if (scorePercentage >= 50 && scorePercentage < 70) {
        remarks = "Good knowledge ";
      } else {
        remarks = "Excellent knowledge";
      }

      const dynamicData = {
        name: firstName,
        score: `${scorePercentage.toFixed(0)}%`,
        key,
        remarks
      };

      await sendMail(userEmail, process.env.TEMPLATE_ID_LITE, dynamicData);
      res.status(200).json({ message: 'Email sent successfully' });
    } else {
      console.error(`User not found for key: ${key}`);
      res.status(404).json({ error: 'User not found' });
    }
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

async function fetchOrGenerateFramework(role) {
  try {
    // Try to retrieve from Firestore
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();
    if (frameworkDoc.exists) {
      console.log(`Framework retrieved from cache for role: ${role}`);
      return frameworkDoc.data();
    }

    console.log(`No cached framework found for role: ${role}. Generating a new one...`);

    // If not found in Firestore, call the generate-framework endpoint
    const response = await axios.post(`http://localhost:${port}/api/generate-framework`, { role });
    return response.data;
  } catch (error) {
    console.error('Error fetching or generating framework:', error);
    return null;
  }
}

async function getRecommendationsFromOpenAIAssistant(role, learningPath, learningPathData, userResponses) {
  try {
    console.log('Analyzing skills with inputs:', {
      role,
      learningPath,
      hasLearningPathData: !!learningPathData,
      responseCount: userResponses?.length
    });

    // Validate required inputs
    if (!role || !learningPath || !learningPathData || !userResponses) {
      throw new Error('Missing required parameters for recommendation generation');
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for recommendations, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    // Separate knowledge check and self-assessment responses
    let knowledgeCheckResponses = [];
    let selfAssessmentResponses = [];

    if (userResponses.responseTypes) {
      knowledgeCheckResponses = userResponses.responseTypes.knowledgeCheck || [];
      selfAssessmentResponses = userResponses.responseTypes.selfAssessment || [];
    } else {
      knowledgeCheckResponses = userResponses.filter(r => r.questionType === 'knowledge-check' || !r.questionType);
      selfAssessmentResponses = userResponses.filter(r => r.questionType === 'self-assessment');
    }

    // Construct the initial analysis context with just user data and role
    const initialAnalysisContext = {
      role,
      assessmentData: {
        knowledgeCheck: {
          responses: knowledgeCheckResponses,
          summary: {
            totalQuestions: knowledgeCheckResponses.length,
            correctAnswers: knowledgeCheckResponses.filter(r => r.isCorrect).length
          }
        },
        selfAssessment: {
          responses: selfAssessmentResponses,
          summary: {
            totalQuestions: selfAssessmentResponses.length,
            skillLevels: categorizeSkillLevels(selfAssessmentResponses)
          }
        }
      }
    };

    // STEP 1: Get competency analysis based on user responses
    const initialInstructions = `
    You are analyzing performance for a ${role} based on assessment responses.

    Your task is to generate a detailed competency analysis with multiple skill areas that are relevant to this role.

    For each skill area:
    1. Determine a proficiency level as a percentage
    2. Identify strength areas
    3. Identify gap areas in the format "SkillArea - Specific Topic" (e.g., "Excel - creating advanced formulas")
    4. Suggest a progression path

    Also provide an overall summary of the user's skills.

    CRITICAL NOTE ABOUT SELF-ASSESSMENT:
    - The assessment includes both knowledge check questions (with correct/incorrect answers) and self-assessment questions (without correct answers).
    - For self-assessment responses, consider the selected option as an indicator of the user's skill level:
      * Option 1 typically indicates beginner/basic level
      * Option 2 typically indicates intermediate level
      * Option 3 typically indicates advanced level
    - Incorporate self-assessment responses when determining competency gaps - if a user selects basic options for a skill area, this indicates a potential training need.

    IMPORTANT: Output Must only be in JSON format. Do not include any other content/explanation or formatting.
    `;

    // Define the function schema for competency analysis - make schema more flexible for GPT-4.1-mini
    const generate_competency_analysis_function = {
      name: "generate_competency_analysis",
      description: "Generate a competency analysis with multiple skill areas, ensuring each gap is 'SkillArea - Specific Topic'.",
      parameters: {
        type: "object",
        properties: {
          competencyAnalysis: {
            type: "object",
            description: "Object containing skill areas as keys, each with proficiency, strengths, gaps, and progression",
            additionalProperties: {
              type: "object",
              properties: {
                proficiencyLevel: { type: "string" },
                strengthAreas: { type: "array", items: { type: "string" } },
                gapAreas: { type: "array", items: { type: "string" } },
                progressionPath: { type: "array", items: { type: "string" } }
              },
              required: ["proficiencyLevel", "strengthAreas", "gapAreas", "progressionPath"]
            }
          },
          summary: { type: "string", description: "Overall summary of the user's skills and development needs" }
        },
        required: ["competencyAnalysis", "summary"]
      }
    };

    // Alternative simpler schema as backup
    const simple_competency_analysis_function = {
      name: "generate_competency_analysis",
      description: "Generate a competency analysis with multiple skill areas",
      parameters: {
        type: "object",
        properties: {
          analysis: {
            type: "object",
            description: "The competency analysis with skill areas"
          },
          summary: {
            type: "string",
            description: "Overall summary of skills"
          }
        },
        required: ["analysis", "summary"]
      }
    };

    // Calculate tokens for initial API call
    const { encode } = require('gpt-tokenizer');
    const initialMessages = [
      { role: "system", content: initialInstructions },
      { role: "user", content: JSON.stringify(initialAnalysisContext) }
    ];

    const initialPromptTokens = initialMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 128000; // GPT-4.1 Mini's context size (estimated)
    const maxResponseTokens = 8000; // GPT-4.1 Mini's max output (estimated)
    const initialAvailableTokens = contextWindow - initialPromptTokens;
    const initial_max_tokens = Math.min(maxResponseTokens, initialAvailableTokens);

    if (initial_max_tokens <= 0) {
      throw new Error(`Initial prompt exceeds context window (${initialPromptTokens}/${contextWindow} tokens)`);
    }

    console.log('Sending competency analysis request to GPT-4.1-mini...');

    // Call GPT-4.1 Mini for competency analysis with extensive debugging
    console.log('Sending competency analysis request to GPT-4.1-mini with detailed instructions...');

    // Try a simple direct approach first - no function calling, just a direct prompt
    try {
      const directPrompt = `
You are analyzing a ${role}'s performance based on assessment responses.

Based on the assessment data provided below, create a detailed competency analysis with at least 3 skill areas.

For each skill area include:
1. A proficiency level (as a percentage)
2. 2-4 strength areas
3. 2-4 gap areas (formatted as "SkillArea - Specific Topic")
4. A progression path

Format your response as valid JSON with this exact structure:
{
  "competencyAnalysis": {
    "Skill Area 1": {
      "proficiencyLevel": "X%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    },
    "Skill Area 2": {
      "proficiencyLevel": "Y%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    },
    "Skill Area 3": {
      "proficiencyLevel": "Z%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    }
  },
  "summary": "Overall summary of performance..."
}

Here is the assessment data:
${JSON.stringify(initialAnalysisContext)}

ONLY respond with the JSON. Do not include any other text.
      `;

      console.log('Trying direct approach without function calling');

      const directCompletion = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          { role: "system", content: directPrompt },
        ],
        max_tokens: initial_max_tokens
      });

      console.log('Direct completion response received');
      const directContent = directCompletion.choices[0].message.content;
      console.log('Raw response content:', directContent);

      // Try to parse the direct response as JSON
      try {
        // Find JSON in the response - look for everything between first { and last }
        const jsonMatch = directContent.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? jsonMatch[0] : directContent;

        console.log('Attempting to parse JSON:', jsonString);
        competencyAnalysis = JSON.parse(jsonString);

        console.log('Successfully parsed direct JSON response');

        // Validate the direct response structure
        if (!competencyAnalysis.competencyAnalysis) {
          console.warn('Direct response missing competencyAnalysis field, trying function calling approach');
          throw new Error('Invalid direct response structure');
        }

        console.log('Direct approach successful, skipping function-calling approach');

      } catch (err) {
        console.warn(`Failed to parse direct response: ${err.message}, trying function calling approach`);
        throw err; // Proceed to function calling approach
      }

    } catch (err) {
      // If direct approach fails, try function calling
      console.log('Falling back to function calling approach');

      const competencyCompletion = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content: `${initialInstructions}\n\nIMPORTANT: Your response MUST include a complete JSON object with both a "competencyAnalysis" object and a "summary" string. Failure to include BOTH of these fields is unacceptable.`
          },
          { role: "user", content: JSON.stringify(initialAnalysisContext) }
        ],
        tools: [{
          type: "function",
          function: simple_competency_analysis_function  // Use the simpler schema
        }],
        tool_choice: { type: "function", function: { name: "generate_competency_analysis" } },
        max_tokens: initial_max_tokens
      });

      console.log('Function calling response received:', JSON.stringify(competencyCompletion, null, 2));

      const competencyMessage = competencyCompletion.choices[0].message;

      // Detailed logging to debug the API response
      if (!competencyMessage.tool_calls || competencyMessage.tool_calls.length === 0) {
        console.error('ERROR: No tool calls found in API response:', JSON.stringify(competencyMessage, null, 2));

        // As a fallback, see if there's content directly in the message
        if (competencyMessage.content) {
          console.log('Found content directly in message, trying to parse:');
          try {
            // Try to extract JSON from the content
            const jsonMatch = competencyMessage.content.match(/\{[\s\S]*\}/);
            const jsonString = jsonMatch ? jsonMatch[0] : competencyMessage.content;

            competencyAnalysis = JSON.parse(jsonString);
            console.log('Successfully parsed JSON from message content');

            // If the JSON doesn't have competencyAnalysis but has analysis, rename it
            if (!competencyAnalysis.competencyAnalysis && competencyAnalysis.analysis) {
              competencyAnalysis.competencyAnalysis = competencyAnalysis.analysis;
              delete competencyAnalysis.analysis;
              console.log('Renamed "analysis" field to "competencyAnalysis"');
            }

          } catch (parseErr) {
            console.error('Failed to parse content as JSON:', parseErr.message);
            throw new Error("Could not generate valid competency analysis");
          }
        } else {
          throw new Error("API response did not include any tool calls or content");
        }
      } else {
        // Process tool calls
        if (competencyMessage.tool_calls[0].function.name !== "generate_competency_analysis") {
          console.error('ERROR: Wrong function called:', competencyMessage.tool_calls[0].function.name);
          throw new Error("API called the wrong function");
        }

        console.log('Function arguments from competency analysis:', competencyMessage.tool_calls[0].function.arguments);

        try {
          competencyAnalysis = JSON.parse(competencyMessage.tool_calls[0].function.arguments);
          console.log('Parsed competency analysis from function arguments');

          // If the JSON doesn't have competencyAnalysis but has analysis, rename it
          if (!competencyAnalysis.competencyAnalysis && competencyAnalysis.analysis) {
            competencyAnalysis.competencyAnalysis = competencyAnalysis.analysis;
            delete competencyAnalysis.analysis;
            console.log('Renamed "analysis" field to "competencyAnalysis"');
          }

        } catch (parseErr) {
          console.error('Failed to parse function arguments:', parseErr.message);
          throw new Error("Could not parse competency analysis from function arguments");
        }
      }
    }

    // Final validation of competency analysis structure
    console.log('Final competency analysis structure:', JSON.stringify(competencyAnalysis, null, 2));

    if (!competencyAnalysis.competencyAnalysis || typeof competencyAnalysis.competencyAnalysis !== 'object') {
      console.error('ERROR: Missing competencyAnalysis object in parsed response:', JSON.stringify(competencyAnalysis, null, 2));

      // Create a fallback competency analysis as a last resort
      console.warn('CREATING FALLBACK COMPETENCY ANALYSIS');
      competencyAnalysis.competencyAnalysis = {
        "Digital Proficiency": {
          "proficiencyLevel": "65%",
          "strengthAreas": [
            "Basic software navigation",
            "Understanding of digital tools",
            "Familiarity with common applications"
          ],
          "gapAreas": [
            `${role} - Advanced features`,
            "Digital Collaboration - Team coordination"
          ],
          "progressionPath": [
            "Basics to intermediate tools",
            "Specialized role-specific applications"
          ]
        },
        "Productivity": {
          "proficiencyLevel": "70%",
          "strengthAreas": [
            "Task management",
            "Basic document creation"
          ],
          "gapAreas": [
            "Office Suite - Advanced functions",
            "Workflow Optimization - Process efficiency"
          ],
          "progressionPath": [
            "Essential office tools",
            "Advanced productivity techniques"
          ]
        },
        "Technical Knowledge": {
          "proficiencyLevel": "60%",
          "strengthAreas": [
            "Fundamental concepts",
            "Standard procedures"
          ],
          "gapAreas": [
            "Technical Skills - Role-specific requirements",
            "Systems Knowledge - Integration capabilities"
          ],
          "progressionPath": [
            "Foundational technical skills",
            "Specialized domain knowledge"
          ]
        }
      };
    }

    // Ensure summary exists with meaningful content
    if (!competencyAnalysis.summary) {
      console.warn('Missing summary field, creating one');
      competencyAnalysis.summary = `Based on the assessment results for a ${role}, several key skill areas require development. Focus should be on building core technical capabilities relevant to the role while enhancing productivity skills. The assessment indicates particular development needs in advanced features of commonly used tools.`;
    }

    // STEP 2: Generate pathway courses text for recommendations
    // Generate formatted course data for each pathway
    const pathwayCoursesText = [];
    globalLearningPathData.pathways.forEach(pathway => {
      // Extract the level number
      const levelMatch = pathway.match(/(\w+)\s+Learning\s+Pathway/i);
      let level = levelMatch ? levelMatch[1] : pathway;

      // Determine numeric sequence for this level
      let sequenceNumber;
      switch(level.toLowerCase()) {
        case 'essentials': sequenceNumber = "1"; break;
        case 'intermediate': sequenceNumber = "2"; break;
        case 'advanced': sequenceNumber = "3"; break;
        case 'champions': sequenceNumber = "4"; break;
        default: sequenceNumber = "";
      }

      pathwayCoursesText.push(`${sequenceNumber} ${level.toUpperCase()} LEARNING PATHWAY\n`);

      // Group courses by category within this pathway
      const categoriesInPathway = {};
      globalLearningPathData.coursesByPathway[pathway].forEach(course => {
        if (!categoriesInPathway[course.category]) {
          categoriesInPathway[course.category] = [];
        }
        categoriesInPathway[course.category].push(course.title);
      });

      // Add formatted category and course listings
      Object.keys(categoriesInPathway).forEach(category => {
        pathwayCoursesText.push(`${category}:`);
        categoriesInPathway[category].forEach(courseTitle => {
          pathwayCoursesText.push(`"${courseTitle}"`);
        });
        pathwayCoursesText.push('');
      });

      pathwayCoursesText.push('');
    });

    // Generate progression patterns text
    const progressionPatternsText = ['Key Complete Progression Patterns (with exact titles):'];

    // Get the most common progression chains
    const mainCourseTypes = [
      'Excel', 'Word', 'PowerPoint', 'Outlook', 'Teams', 'SharePoint', 'Cyber Security'
    ];

    mainCourseTypes.forEach(baseType => {
      const chains = [];
      globalLearningPathData.allCourses.forEach(course => {
        if (course.title.startsWith(baseType)) {
          chains.push(course.title);
        }
      });

      if (chains.length > 0) {
        // Sort by pathway
        chains.sort((a, b) => {
          const pathwayOrder = {
            'Essentials': 1,
            'Intermediate': 2,
            'Advanced': 3,
            'Champions': 4
          };

          const aLevel = a.split(' - ')[1];
          const bLevel = b.split(' - ')[1];
          return pathwayOrder[aLevel] - pathwayOrder[bLevel];
        });

        progressionPatternsText.push(`${baseType}:`);
        progressionPatternsText.push(`"${chains.join('" → "')}"`, '');
      }
    });

    // Create recommendation context with competency analysis and learning path data
    const recommendationContext = {
      role,
      currentPath: learningPath,
      competencyAnalysis: competencyAnalysis.competencyAnalysis,
      summary: competencyAnalysis.summary
    };

    console.log('Creating recommendation context with competency analysis:',
      JSON.stringify(recommendationContext, null, 2));

    // Instructions for course recommendations
    const recommendationInstructions = `
    You are generating course recommendations for a ${role} in the ${learningPath} learning path.

    IMPORTANT OVERVIEW:
    - If a user is currently at the "Essentials" path, recommend relevant courses from "Essentials" and then include higher-level courses ("Intermediate" → "Advanced" → "Champions") that build upon those skills.
    - If a user is currently at the "Intermediate" path, recommend only "Intermediate" courses and then potential progressions from "Advanced" → "Champions" (never below "Intermediate").
    - If a user is currently at the "Advanced" path, recommend only "Advanced" courses and then potential progressions from "Champions" (never below "Advanced").
    - If a user is at the "Champions" path, do not recommend any additional courses.
    - The progression should be complete all the way to "Champions" for any user level below "Champions."
      * For example, if recommending "Excel - Essentials" (at Essentials level), you must also recommend "Excel - Intermediate" (Intermediate) and "Excel - Advanced" (Advanced).
      * Similarly, if a user is at the "Intermediate" level for Excel, you must still include relevant Advanced-level (and Champions-level if applicable) courses from the learning-path-data.

    CRITICAL REQUIREMENTS FOR COURSE RECOMMENDATIONS:
    1. You MUST ONLY recommend courses that exist in the learning-path-data.
    2. Course names in recommendations MUST EXACTLY match the "title" field from the courses in the learning-path-data.
    3. Do not invent or suggest courses that don't exist in the learning path data.
    4. For cross-path recommendations:
       - When recommending a course in a specific area (e.g., Excel), you MUST include related progressive courses from other paths — but only at or above the user's current level.
       - For example: If recommending "Excel - Essentials" (Essentials), also suggest "Excel - Intermediate" (Intermediate) and "Excel - Advanced" (Advanced).
       - Ensure all recommendations form a clear learning progression path.
       - Only suggest courses from higher-level paths that build upon the base recommendation — not lower levels.

    ADDITIONAL CRITICAL NOTE #1:
    - If the competency analysis or summary identifies a specific gap by name (e.g., "Planner"), and it exists in the learning-path data, you MUST include it in "recommendations" **if**:
      (a) The user's current path level allows it (no recommending from a level below the user's current level).
      (b) It does not violate the progression rules (if the user is at Essentials, you can still recommend that advanced course by including the appropriate progression from Intermediate → Advanced, etc.).
    - Gaps explicitly mentioned should **always** be addressed in the "recommendations" or in "other_learning_paths_courses" if it is at a higher level than the user's path.

    Below is the learning path data:

    ${pathwayCoursesText.join('\n')}

    ${progressionPatternsText.join('\n')}

    IMPORTANT: Output Must only be in JSON format. Do not include any other content/explanation or formatting.

    You MUST include the following in your response:
    1. An "recommendations" array with entries for the user's current path level
    2. An "other_learning_paths_courses" array with appropriate higher-level courses
    3. A "validation_rules" object with progression information

    Each component is REQUIRED in your response.
    `;

    // Define the function schema for recommendations
    const generate_recommendations_function = {
      name: "generate_recommendations",
      description: "Generate course recommendations based on competency analysis.",
      parameters: {
        type: "object",
        properties: {
          recommendations: {
            type: "array",
            items: {
              type: "object",
              properties: {
                course: { type: "string" },
                reason: { type: "string" },
                skillArea: { type: "string" }
              },
              required: ["course", "reason", "skillArea"]
            }
          },
          other_learning_paths_courses: {
            type: "array",
            items: {
              type: "object",
              properties: {
                course: { type: "string" },
                learningPath: { type: "string" },
                reason: { type: "string" },
                skillArea: { type: "string" }
              },
              required: ["course", "learningPath", "reason", "skillArea"]
            }
          },
          validation_rules: {
            type: "object",
            properties: {
              progression_order: { type: "array", items: { type: "string" } },
              example_valid_paths: { type: "array", items: { type: "string" } },
              prohibited: { type: "string" }
            },
            required: ["progression_order", "example_valid_paths", "prohibited"]
          }
        },
        required: [
          "recommendations",
          "other_learning_paths_courses",
          "validation_rules"
        ]
      }
    };

    // Calculate tokens for recommendation API call
    const recommendationMessages = [
      { role: "system", content: recommendationInstructions },
      { role: "user", content: JSON.stringify(recommendationContext) }
    ];

    const recommendationPromptTokens = recommendationMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const recommendationAvailableTokens = contextWindow - recommendationPromptTokens;
    const recommendation_max_tokens = Math.min(maxResponseTokens, recommendationAvailableTokens);

    if (recommendation_max_tokens <= 0) {
      throw new Error(`Recommendation prompt exceeds context window (${recommendationPromptTokens}/${contextWindow} tokens)`);
    }

    // Improved recommendation API call - more explicit instructions and validation
    console.log('Sending recommendation request to GPT-4.1-mini...');
    const recommendationCompletion = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content: `${recommendationInstructions}\n\nCRITICAL: Your response MUST include all three required fields: "recommendations" array, "other_learning_paths_courses" array, and "validation_rules" object. Failure to include ANY of these fields is unacceptable.`
        },
        { role: "user", content: JSON.stringify(recommendationContext) }
      ],
      tools: [{
        type: "function",
        function: generate_recommendations_function
      }],
      tool_choice: { type: "function", function: { name: "generate_recommendations" } },
      max_tokens: recommendation_max_tokens
    });

    console.log('Recommendation response received from GPT-4.1-mini');

    const recommendationMessage = recommendationCompletion.choices[0].message;

    // Detailed logging and validation
    if (!recommendationMessage.tool_calls || recommendationMessage.tool_calls.length === 0) {
      console.error('ERROR: No tool calls found in recommendation response:', JSON.stringify(recommendationMessage, null, 2));
      throw new Error("Recommendation API response did not include any tool calls");
    }

    if (recommendationMessage.tool_calls[0].function.name !== "generate_recommendations") {
      console.error('ERROR: Wrong function called for recommendations:', recommendationMessage.tool_calls[0].function.name);
      throw new Error("API called the wrong function for recommendations");
    }

    // Log the arguments to debug the content
    console.log('Function arguments from recommendations:', recommendationMessage.tool_calls[0].function.arguments);

    let recommendations;
    try {
      recommendations = JSON.parse(recommendationMessage.tool_calls[0].function.arguments);

      // Verify we have the expected structure
      if (!Array.isArray(recommendations.recommendations)) {
        console.error('ERROR: Missing or invalid recommendations array:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required recommendations array");
      }

      if (!Array.isArray(recommendations.other_learning_paths_courses)) {
        console.error('ERROR: Missing or invalid other_learning_paths_courses array:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required other_learning_paths_courses array");
      }

      if (!recommendations.validation_rules || typeof recommendations.validation_rules !== 'object') {
        console.error('ERROR: Missing or invalid validation_rules object:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required validation_rules object");
      }

      // Verify we have some recommendations if gaps were identified
      if (recommendations.recommendations.length === 0) {
        const hasGaps = Object.values(competencyAnalysis.competencyAnalysis).some(
          area => area.gapAreas && area.gapAreas.length > 0
        );

        if (hasGaps) {
          console.error('ERROR: No recommendations provided despite identified gaps');
          throw new Error("API response contains no recommendations despite identified skill gaps");
        }
      }

      console.log('Successfully validated recommendations with',
        recommendations.recommendations.length, 'current path recommendations and',
        recommendations.other_learning_paths_courses.length, 'cross-path recommendations');

    } catch (err) {
      console.error(`Failed to parse or validate recommendations: ${err.message}`);
      throw new Error(`Invalid recommendations from API: ${err.message}`);
    }

    // Helper: Apply progression logic by encapsulating it into its own function
    function applyProgressionLogic(parsedObj) {
      // Use the dynamically generated progression chains
      const progressionChains = globalLearningPathData.progressionChains || {};

      function getNextCourses(courseTitle) {
        return progressionChains[courseTitle] || [];
      }

      function addCourse(parsedObj, courseTitle, skillArea, reason, targetPath) {
        const alreadyInRecommendations = parsedObj.recommendations.some(r => r.course === courseTitle);
        const alreadyInOther = parsedObj.other_learning_paths_courses.some(c => c.course === courseTitle);
        if (!alreadyInRecommendations && !alreadyInOther) {
          parsedObj.other_learning_paths_courses.push({
            course: courseTitle,
            learningPath: targetPath,
            reason,
            skillArea
          });
        }
      }

      function determineLearningPath(courseTitle) {
        if (courseTitle.includes("Essentials")) return "Essentials";
        if (courseTitle.includes("Intermediate")) return "Intermediate";
        if (courseTitle.includes("Advanced")) return "Advanced";
        if (courseTitle.includes("Champions")) return "Champions";
        return "Unknown";
      }

      for (const rec of parsedObj.recommendations) {
        let currentCourse = rec.course;
        let nextCourses = getNextCourses(currentCourse);
        while (nextCourses.length > 0) {
          const nextCourse = nextCourses[0];
          const reason = `To advance ${currentCourse} skills.`;
          const targetPath = determineLearningPath(nextCourse);
          addCourse(parsedObj, nextCourse, rec.skillArea, reason, targetPath);
          currentCourse = nextCourse;
          nextCourses = getNextCourses(currentCourse);
        }
      }
    }

    // Apply the progression logic helper to the parsed object
    applyProgressionLogic(recommendations);

    // Combine the competency analysis and recommendations into a single result
    // No default values - maintain exact structure with actual data
    const finalResult = {
      report: {
        competencyAnalysis: competencyAnalysis.competencyAnalysis,
        summary: competencyAnalysis.summary
      },
      recommendations: recommendations.recommendations,
      other_learning_paths_courses: recommendations.other_learning_paths_courses,
      validation_rules: recommendations.validation_rules
    };

    console.log('Successfully generated multi-skill-area recommendations with progression checks.');
    return finalResult;
  } catch (error) {
    console.error('Error in recommendation generation:', {
      error: error.message,
      role,
      learningPath,
      errorType: error.constructor.name
    });
    throw error;
  }
}

function categorizeSkillLevels(selfAssessmentResponses) {
  // Group responses by skill area
  const skillAreas = {};

  selfAssessmentResponses.forEach(response => {
    if (response.selectedAnswer === 'SKIPPED') return;

    const skillArea = response.skillArea || 'General';
    if (!skillAreas[skillArea]) {
      skillAreas[skillArea] = [];
    }

    skillAreas[skillArea].push({
      question: response.question,
      skillLevel: response.skillLevel || 1,
      selectedAnswer: response.selectedAnswer
    });
  });

  // Calculate average skill level for each area
  const skillLevelSummary = {};

  Object.entries(skillAreas).forEach(([area, responses]) => {
    if (responses.length === 0) return;

    const totalLevel = responses.reduce((sum, r) => sum + r.skillLevel, 0);
    const avgLevel = totalLevel / responses.length;

    let skillCategory;
    if (avgLevel < 1.5) {
      skillCategory = 'Basic';
    } else if (avgLevel < 2.5) {
      skillCategory = 'Intermediate';
    } else {
      skillCategory = 'Advanced';
    }

    skillLevelSummary[area] = {
      averageLevel: avgLevel,
      category: skillCategory,
      responses: responses.length
    };
  });

  return skillLevelSummary;
}


async function updateFirestoreWithRecommendations(userEmail, analysisResults, userCompany) {
  console.log('Starting Firestore update with parameters:', {
    userEmail,
    userCompany,
    hasAnalysis: !!analysisResults,
    hasOtherPathRecommendations: !!analysisResults?.other_learning_paths_courses
  });

  if (!userEmail?.trim()) {
    throw new Error('Invalid or missing user email');
  }
  if (!userCompany?.trim()) {
    throw new Error('Invalid or missing company name');
  }
  if (!analysisResults) {
    throw new Error('Missing analysis results');
  }

  const sanitizedEmail = userEmail.trim();
  const sanitizedCompany = userCompany.trim();

  try {
    // For student-focused version, automatically create Birmingham company if it doesn't exist
    const companyDoc = await firestore
      .collection('companies')
      .doc(sanitizedCompany)
      .get();

    if (!companyDoc.exists && sanitizedCompany === 'Birmingham') {
      console.log('Creating Birmingham company document for student users');
      await firestore
        .collection('companies')
        .doc(sanitizedCompany)
        .set({
          name: 'Birmingham',
          type: 'student-focused',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          description: 'Auto-created company for student assessments'
        });
    } else if (!companyDoc.exists) {
      throw new Error(`Company not found: ${sanitizedCompany}`);
    }

    // Get user reference (user should exist from form submission)
    const userRef = firestore
      .collection('companies')
      .doc(sanitizedCompany)
      .collection('users')
      .doc(sanitizedEmail);

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
      throw new Error(`User not found in company: ${sanitizedEmail}`);
    }

    const userRole = userDoc.data().userRole ?? null;
    const currentPath = userDoc.data().currentPath ?? null;

    // Create a new assessment results document
    const assessmentResultRef = userRef.collection('assessmentResults').doc();

    // Store the complete analysis results including other learning paths
    await assessmentResultRef.set({
      competencyAnalysis: analysisResults.report.competencyAnalysis,
      analysisSummary: analysisResults.report.summary,
      courseRecommendations: analysisResults.recommendations.map(rec => ({
        courseName: rec.course,
        justification: rec.reason,
        isCurrentPath: true
      })),
      otherPathRecommendations: analysisResults.other_learning_paths_courses?.map(rec => ({
        courseName: rec.course,
        justification: rec.reason,
        learningPath: rec.learningPath,
        isCurrentPath: false
      })) || [],
      metadata: {
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        role: userRole,
        learningPath: currentPath
      }
    });

    // Update the user document with both types of recommendations
    await userRef.update({
      lastAssessmentId: assessmentResultRef.id,
      lastAssessmentDate: admin.firestore.FieldValue.serverTimestamp(),
      skillsAnalysis: analysisResults.report.summary,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),

      // Primary course recommendations (maintaining the original structure)
      courseRecommendations: analysisResults.recommendations.map(rec => ({
        course: rec.course,
        reason: rec.reason
      })),

      // Add other learning paths recommendations
      otherPathRecommendations: analysisResults.other_learning_paths_courses?.map(rec => ({
        course: rec.course,
        reason: rec.reason,
        learningPath: rec.learningPath
      })) || []
    });

    console.log('Successfully updated recommendations for:', {
      email: sanitizedEmail,
      company: sanitizedCompany,
      primaryRecsCount: analysisResults.recommendations.length,
      otherPathRecsCount: analysisResults.other_learning_paths_courses?.length || 0
    });

  } catch (error) {
    console.error('Firestore update failed:', error);
    throw new Error(`Firestore update failed: ${error.message}`);
  }
}

app.post('/api/assessment-result', async (req, res) => {
  console.log('Received assessment result:', JSON.stringify(req.body, null, 2));

  try {
    const { userEmail, role, userCompany, assessmentResult } = req.body;

    // Validate required fields
    if (!userEmail || !userCompany || !role || !assessmentResult?.learningPath) {
      console.error('Missing required fields:', { userEmail, userCompany, role, learningPath: assessmentResult?.learningPath });
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          userEmail: !userEmail ? 'Missing email' : null,
          userCompany: !userCompany ? 'Missing company' : null,
          role: !role ? 'Missing role' : null,
          learningPath: !assessmentResult?.learningPath ? 'Missing learning path' : null
        }
      });
    }

    // Ensure we have a framework before proceeding
    let framework = assessmentResult.framework;
    if (!framework) {
      console.log('No framework provided in assessment result. Attempting to fetch or generate...');
      framework = await fetchOrGenerateFramework(role);
      if (!framework) {
        return res.status(500).json({
          error: 'Unable to retrieve or generate framework. Please try again later.'
        });
      }
    }

    // Get recommendations with the provided or retrieved framework
    const analysisResults = await getRecommendationsFromOpenAIAssistant(
      role,
      assessmentResult.learningPath,
      framework,
      assessmentResult.userResponses
    );

    // Update Firestore with the complete analysis
    await updateFirestoreWithRecommendations(
      userEmail,
      analysisResults,
      userCompany
    );

    res.status(200).json({
      success: true,
      message: 'Assessment results and recommendations processed successfully',
      analysis: analysisResults
    });

  } catch (error) {
    console.error('Error in /api/assessment-result:', error);
    res.status(500).json({
      error: 'Failed to process assessment result',
      details: error.message
    });
  }
});

// Firebase Realtime Database listeners
const resultsRef = db.ref('results');
const resultsv2Ref = db.ref('resultsv2');

resultsRef.on('child_added', async (snapshot) => {
  processResult(snapshot);
});

resultsv2Ref.on('child_added', async (snapshot) => {
  processResult(snapshot, true);
});

// Helper function to process the result
const processResult = async (snapshot, isResultsV2 = false) => {
  const resultId = snapshot.key;
  const result = snapshot.val();

  const processedResultSnapshot = await processedResultsRef.child(resultId).once('value');
  if (processedResultSnapshot.exists()) {
    return;
  }

  const { email, section, score, totalQuestions, firstName, lastName, role, isNewUser, userCompany } = result;

  if (isNewUser) {
    let templateId;

    if (isResultsV2) {
      switch (section) {
        case 'Essentials': templateId = process.env.TEMPLATE_IDV2_1; break;
        case 'Intermediate': templateId = process.env.TEMPLATE_IDV2_2; break;
        case 'Advanced': templateId = process.env.TEMPLATE_IDV2_3; break;
        case 'Champions': templateId = process.env.TEMPLATE_IDV2_4; break;
        default: templateId = process.env.TEMPLATE_IDV2_1;
      }
    } else {
      switch (section) {
        case 'Essentials': templateId = process.env.TEMPLATE_ID_1; break;
        case 'Intermediate': templateId = process.env.TEMPLATE_ID_2; break;
        case 'Advanced': templateId = process.env.TEMPLATE_ID_3; break;
        case 'Champions': templateId = process.env.TEMPLATE_ID_4; break;
        default: templateId = process.env.TEMPLATE_ID_1;
      }
    }

    const scorePercentage = (score / totalQuestions) * 100;

    const dynamicData = {
      name: `${firstName} ${lastName}`,
      role,
      section,
      company: userCompany,
      score: `${scorePercentage.toFixed(0)}%`
    };

    try {
      await sendMail(email, templateId, dynamicData);
      await processedResultsRef.child(resultId).set(true);
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }
};

// Updated role validation endpoint using new OpenAI API syntax
app.post('/api/validate-role', async (req, res) => {
  const { role } = req.body;
  const cacheKey = role.toLowerCase().trim();

  // Check cache first
  if (roleCache.has(cacheKey)) {
    const isValid = roleCache.get(cacheKey);
    return res.json({ isValid });
  }

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are a UK employment expert who validates job titles. Accept both traditional professions and modern job roles. A valid job title can be either an established profession or a role with a descriptive suffix. Focus on whether it represents a genuine occupation someone can be employed as.`
        },
        {
          role: 'user',
          content: `Is "${role}" a valid job title/role? Consider it valid if it meets ANY of these criteria:

1. It's an established profession or occupation (e.g., Doctor, Nurse, Architect, Teacher, Medic)
2. It's a modern job role that typically includes suffixes like:
   - Manager, Director, Coordinator, Assistant
   - Engineer, Developer, Designer
   - Specialist, Professional, Consultant
   - Coach, Trainer, Officer, Lead
3. It describes a specific job function (not a department, facility, or technology)

Examples:
✓ VALID:
- Traditional professions: "Doctor", "Architect", "Teacher", "Nurse", "Medic"
- Modern roles: "Data Manager", "Business Coach", "IT Specialist"
- Specific functions: "Accountant", "Researcher", "Analyst"

✗ INVALID:
- Departments alone: "Marketing", "Sales", "IT"
- Facilities: "Data Center", "Office"
- Generic terms: "Leadership", "Technology"
- Technologies: "Microsoft Excel", "JavaScript"

Respond ONLY with "valid" or "invalid".`
        }
      ],
      max_tokens: 1,
      temperature: 0
    });

    const assistantResponse = completion.choices[0].message.content.toLowerCase().trim();
    const isValid = assistantResponse === 'valid';

    // Store in cache
    roleCache.set(cacheKey, isValid);

    res.json({ isValid });
  } catch (error) {
    console.error('Error validating role:', error.response ? error.response.data : error.message);
    res.status(500).json({ error: 'Error validating role' });
  }
});


app.post('/api/generate-framework', async (req, res) => {
  const { role, email } = req.body;

  if (!role) {
    return res.status(400).json({ error: 'Role is required' });
  }

  console.log('Generating framework for role:', role);

  // Check if this is a student user
  if (role.toLowerCase().includes('student')) {
    console.log('Student user detected, returning predefined student framework');
    const studentFramework = getStudentFramework();

    // Cache the student framework
    try {
      await firestore.collection('frameworks').doc(role).set(studentFramework);
      console.log('Student framework cached for role:', role);
    } catch (error) {
      console.error('Error caching student framework:', error);
    }

    return res.json(studentFramework);
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-framework', { role })) {
    console.log(`Duplicate framework request detected for ${email}, role: ${role}`);

    // Try to return cached response
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();
    if (frameworkDoc.exists) {
      return res.json(frameworkDoc.data());
    }
  }

  // Define the function in the format expected by o3-mini
  const generate_competency_framework = {
    type: "function",
    function: {
      name: "generate_competency_framework",
      description: "Generate a competency framework for a given role.",
      parameters: {
        type: "object",
        properties: {
          role: {
            type: "object",
            properties: {
              title: { type: "string" },
              description: { type: "string" }
            },
            required: ["title", "description"]
          },
          coreCompetencies: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string" },
                title: { type: "string" },
                requiredSkills: {
                  type: "array",
                  items: { type: "string" }
                }
              },
              required: ["id", "title", "requiredSkills"]
            }
          },
          developmentPath: {
            type: "object",
            properties: {
              levels: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: {
                      type: "string",
                      enum: ["Essentials", "Intermediate", "Advanced", "Champions"]
                    },
                    focus: { type: "string" },
                    outcomes: {
                      type: "array",
                      items: { type: "string" }
                    }
                  },
                  required: ["name", "focus", "outcomes"]
                }
              }
            },
            required: ["levels"]
          },
          successMetrics: {
            type: "array",
            items: { type: "string" }
          }
        },
        required: ["role", "coreCompetencies", "developmentPath", "successMetrics"]
      }
    }
  };

  try {
    // Check if a cached framework exists in Firestore
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();

    if (frameworkDoc.exists) {
      // Cached framework found, retrieve it from Firestore
      const cachedFramework = frameworkDoc.data();
      console.log('Cached framework found for role:', role);
      return res.json(cachedFramework);
    }

    // Make sure learning path data is loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        return res.status(500).json({ error: 'Learning path data could not be loaded' });
      }
    }

    // Prepare the course catalog information
    const categoryInfo = [];

    // Format the course data for the prompt
    Object.keys(globalLearningPathData.coursesByCategory).forEach(category => {
      const courses = globalLearningPathData.coursesByCategory[category];

      // Group by pathway
      const coursesByPathway = {};
      courses.forEach(course => {
        if (!coursesByPathway[course.pathway]) {
          coursesByPathway[course.pathway] = [];
        }
        coursesByPathway[course.pathway].push(course.title);
      });

      // Create formatted category string
      let categoryText = `${category.toUpperCase()}:\n`;
      Object.keys(coursesByPathway).forEach(pathway => {
        const pathwayCourses = coursesByPathway[pathway];
        pathwayCourses.forEach(course => {
          categoryText += `- "${course}"\n`;
        });
      });

      categoryInfo.push(categoryText);
    });

    const coursesCatalogText = categoryInfo.join('\n');

    // Calculate token usage to ensure we're within model limits
    const { encode } = require('gpt-tokenizer');
    const messages = [
      {
        role: "system",
        content: "You are a role analysis expert. Create a competency framework that maps job competencies to our learning catalog."
      },
      {
        role: "user",
        content: `For the role of "${role}", create a competency framework by identifying the core competencies required for this role. For each competency, map only the necessary courses from our learning catalog that are directly relevant to developing that competency.

        Available courses in our learning catalog:

        ${coursesCatalogText}

        Important Instructions:
        - Only include courses that are essential for developing each competency.
        - Do not include unnecessary courses that do not directly contribute to the competency or daily work for the role.
        - In the "requiredSkills" array, for each course included, provide a brief explanation of why it is linked to the competency or how it will help the person excel. Format each entry as "Course Name - Brief Explanation".
        - Ensure the output JSON exactly matches the specified structure.
        - You must only recommend courses that exist in the catalog above, with exact matching names.`
      }
    ];

    const promptTokens = messages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 100000; // o3-mini's max output
    const availableTokens = contextWindow - promptTokens;

    if (availableTokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    // Cached framework not found, generate a new one using OpenAI API with o3-mini
    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: [
        {
          role: "system",
          content: "You are a role analysis expert. Create a competency framework that maps job competencies to our learning catalog."
        },
        {
          role: "user",
          content: `For the role of "${role}", create a competency framework by identifying the core competencies required for this role. For each competency, map only the necessary courses from our learning catalog that are directly relevant to developing that competency.

          Available courses in our learning catalog:

          ${coursesCatalogText}

          Important Instructions:
          - Only include courses that are essential for developing each competency.
          - Do not include unnecessary courses that do not directly contribute to the competency or daily work for the role.
          - In the "requiredSkills" array, for each course included, provide a brief explanation of why it is linked to the competency or how it will help the person excel. Format each entry as "Course Name - Brief Explanation".
          - Ensure the output JSON exactly matches the specified structure.
          - You must only recommend courses that exist in the catalog above, with exact matching names.`
        }
      ],
      tools: [generate_competency_framework],
      tool_choice: { type: "function", function: { name: "generate_competency_framework" } },
      max_completion_tokens: Math.min(maxResponseTokens, availableTokens),
      reasoning_effort: "low" // Using low reasoning effort as requested
    });

    const message = completion.choices[0].message;

    if (message.tool_calls && message.tool_calls.length > 0 &&
        message.tool_calls[0].function.name === "generate_competency_framework") {

      const functionArgs = JSON.parse(message.tool_calls[0].function.arguments);

      // Validate pathway names
      const validPathways = new Set(["Essentials", "Intermediate", "Advanced", "Champions"]);
      const pathwaysAreValid = functionArgs.developmentPath.levels.every((level) =>
        validPathways.has(level.name)
      );

      if (!pathwaysAreValid) {
        throw new Error("Invalid pathway names detected");
      }

      // Cache the result in Firestore
      await firestore.collection('frameworks').doc(role).set(functionArgs);

      // Track this request to prevent duplicates
      if (email) {
        trackRequest(email, 'generate-framework', { role });
      }

      console.log('Generated and cached framework for role:', role);
      res.json(functionArgs);
    } else {
      throw new Error("Assistant did not call the expected function");
    }
  } catch (error) {
    console.error("Error generating framework:", error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-framework', { role });
    }
    res.status(500).json({ error: "Failed to generate framework" });
  }
});

app.post('/api/generate-quiz', async (req, res) => {
  console.log('Received quiz generation request:', {
    role: req.body.role,
    section: req.body.section,
    email: req.body.email, // Log email for debugging
    isStudent: req.body.role?.toLowerCase().includes('student')
  });

  const { role, section, framework, email } = req.body;

  if (!role || !section || !framework) {
    console.log('Missing required fields:', { role, section, framework: !!framework });
    return res.status(400).json({
      error: 'Missing required fields',
      details: {
        role: !role ? 'Missing role' : undefined,
        section: !section ? 'Missing section' : undefined,
        framework: !framework ? 'Missing framework' : undefined
      }
    });
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-quiz', { role, section })) {
    console.log(`Duplicate quiz request detected for ${email}`);

    // Try to return cached questions
    const cachedQuestions = await getCachedQuestions(role, section);
    if (cachedQuestions?.length === 10) {
      return res.json(cachedQuestions);
    }
  }

  try {
    // Check cache first
    const cachedQuestions = await getCachedQuestions(role, section);
    if (cachedQuestions?.length === 10) {
      console.log('Retrieved questions from cache for:', { role, section });
      return res.json(cachedQuestions);
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for quiz generation, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    console.log('No cached questions found, generating new questions');
    const difficultyLevel = getDifficultyLevel(section);

    // Check if this is a student user and handle differently
    const isStudent = role.toLowerCase().includes('student');
    let courses;

    if (isStudent) {
      console.log('Student user detected, using student-specific course extraction');
      courses = extractStudentCoursesForSection(section, globalLearningPathData);
      console.log('Student courses extracted:', courses);

      // Fallback for students if no courses found
      if (courses.length === 0) {
        console.log('No student courses found, using fallback courses');
        courses = ['Computer Skills – Beginners', 'Computer Skills – Beginners Plus'];
      }
    } else {
      // Extract relevant courses for the role and section (professional users)
      const relevantCourses = extractRelevantCoursesForRoleAndSection(role, section, framework, globalLearningPathData);
      // If we don't have enough relevant courses, fall back to extracting from framework
      courses = relevantCourses.length >= 3 ? relevantCourses : extractCoursesFromFramework(framework);

      // Final validation for professional users
      if (courses.length === 0) {
        console.warn('No courses found for question generation');
        return res.status(400).json({ error: 'No relevant courses found for this role and section' });
      }
    }

    // High-quality example questions with diverse scenario formats
    let exampleQuestions;

    if (isStudent) {
      // Student-focused example questions
      exampleQuestions = [
        {
          question: "What is the first step when turning on a computer for the first time?",
          options: [
            "Open a web browser",
            "Press the power button",
            "Connect to WiFi",
            "Create a user account"
          ],
          answer: "Press the power button",
          course: "Computer Skills – Beginners"
        },
        {
          question: "Which key combination is used to copy text in most applications?",
          options: [
            "Ctrl + V",
            "Ctrl + C",
            "Ctrl + X",
            "Ctrl + Z"
          ],
          answer: "Ctrl + C",
          course: "Computer Skills – Beginners Plus"
        },
        {
          question: "When you want to save a file you've been working on, what should you do?",
          options: [
            "Turn off the computer immediately",
            "Close the program without saving",
            "Use Ctrl + S or click the Save button",
            "Delete the file"
          ],
          answer: "Use Ctrl + S or click the Save button",
          course: "Computer Skills – Improvers Plus"
        }
      ];
    } else {
      // Professional-focused example questions
      exampleQuestions = [
        {
          question: "Which feature should be configured to ensure only team leads can post announcements in a specific channel?",
          options: [
            "Channel notifications",
            "Channel moderation settings",
            "Channel email address",
            "Channel tabs"
          ],
          answer: "Channel moderation settings",
          course: "Teams - Channels"
        },
        {
          question: "During a large presentation meeting, what setting prevents attendees from unmuting themselves?",
          options: [
            "Allow mic for attendees",
            "Who can present?",
            "Meeting recording",
            "Live reactions"
          ],
          answer: "Who can present?",
          course: "Teams - Meetings"
        },
        {
          question: "When a suspicious file is detected, which Defender feature automatically quarantines it?",
          options: [
            "AutoInvestigate",
            "Threat & Vulnerability Management",
            "Real-time protection",
            "Secure Score"
          ],
          answer: "Real-time protection",
          course: "Microsoft Defender"
        }
      ];
    }

    // Calculate target distribution for balanced questions
    const targetDistribution = calculateTargetDistribution(courses);

    // Calculate token usage for the prompt
    const { encode } = require('gpt-tokenizer');

    // Create appropriate prompt based on user type
    let diversePrompt;

    if (isStudent) {
      // Student-focused prompt
      diversePrompt = `You are an expert assessment designer specializing in digital literacy and computer skills evaluation for students. Your goal is to create clear, educational questions that assess fundamental computer and digital skills without being intimidating.

STUDENT ASSESSMENT DESIGN PRINCIPLES:

1. CLEAR AND SIMPLE LANGUAGE:
   - Use straightforward, easy-to-understand language
   - Avoid technical jargon unless it's being taught
   - Focus on practical, everyday computer tasks
   - Make questions relevant to academic and personal use

2. DIVERSE QUESTION FORMATS:
   - "What should you do when..."
   - "Which option is used to..."
   - "How do you..."
   - "What happens when you..."
   - "Which key combination..."

3. EDUCATIONAL FOCUS:
   - Questions should teach as well as assess
   - Focus on fundamental skills that build confidence
   - Include basic computer operations, file management, and common applications
   - Cover digital safety and responsible technology use

4. ANSWER OPTIONS DESIGN:
   - The correct answer should be clearly the best choice
   - Wrong answers should be obviously incorrect to confident users
   - Include common beginner mistakes as distractors
   - Use simple, clear terminology

5. SKILL PROGRESSION: Questions should match the learning level (${difficultyLevel}) and help students build from basic to more advanced skills.

Here are examples of excellent student assessment questions:

${JSON.stringify(exampleQuestions, null, 2)}`;
    } else {
      // Professional-focused prompt
      diversePrompt = `You are an expert assessment designer specializing in Microsoft 365 and cybersecurity skills evaluation. Your goal is to create varied, scenario-based questions that accurately assess specific skills without being repetitive.

ASSESSMENT DESIGN PRINCIPLES:

1. DIVERSE QUESTION FORMATS:
   - DO NOT start every question with "[Role] needs to..." or "[Role] wants to..."
   - Instead, use a variety of sentence structures:
     * "Which feature allows users to..."
     * "What is the most efficient way to..."
     * "To accomplish [task], which setting should be used?"
     * "When [situation occurs], what is the appropriate action?"

2. CLEAR PROBLEM STATEMENT:
   - Focus on the task or problem itself, not the person doing it
   - Make the scenario relevant to a ${role}'s work without repeatedly mentioning the role
   - Use active voice and direct questions

3. ANSWER OPTIONS DESIGN:
   - The correct answer should directly solve the scenario problem
   - Distractors (wrong answers) must be plausible alternatives that:
     * Reflect common misconceptions
     * Use correct terminology but apply it incorrectly
     * Represent suboptimal approaches that seem reasonable

4. QUESTION PRECISION:
   - Questions should be precise and clear
   - Include enough context for the scenario to make sense
   - No arbitrary length restrictions - use as many words as needed to clearly present the scenario

5. DIFFERENTIATION: Questions should distinguish between basic familiarity and mastery of tools.

Here are examples of excellent assessment questions with diverse formats:

${JSON.stringify(exampleQuestions, null, 2)}`;
    }

    const baseMessages = [
      {
        role: "system",
        content: diversePrompt
      },
      {
        role: "user",
        content: `Create 10 high-quality assessment questions relevant to a ${role} at the ${difficultyLevel} level.

IMPORTANT: Use varied question formats! DO NOT begin questions with "${role}..." or similar patterns.

Ensure each question:
1. Presents a specific workplace scenario using varied sentence structures and formats
2. Tests a distinct skill from the courses relevant to this role
3. Has one clearly correct answer and three plausible but incorrect options
4. Is precise and as detailed as necessary to clearly present the scenario

The questions should cover these skill areas evenly: ${courses.join(', ')}.`
      }
    ];

    const promptTokens = baseMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 12000; // Allowing more tokens for quality responses
    const availableTokens = contextWindow - promptTokens;

    if (availableTokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    // Define the generate_quiz_questions function for o3-mini
    const generate_quiz_questions = {
      type: "function",
      function: {
        name: "generate_quiz_questions",
        description: "Generate high-quality assessment questions",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: {
                    type: "string",
                    description: "A precise scenario-based question with varied format (not always starting with the role)"
                  },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 4,
                    maxItems: 4,
                    description: "Four plausible options with one correct answer"
                  },
                  answer: {
                    type: "string",
                    description: "The correct answer (must match exactly one of the options)"
                  },
                  course: {
                    type: "string",
                    description: "The specific skill area or course being tested"
                  }
                },
                required: ["question", "options", "answer", "course"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    let allQuestions = [];
    let retryCount = 0;
    const maxRetries = 2;

    while (allQuestions.length < 10 && retryCount < maxRetries) {
      console.log(`Attempt ${retryCount + 1}: Generating diverse questions with o3-mini`);

      // Adaptive prompting based on what's already been generated
      const targetCount = 10 - allQuestions.length;
      const missingCourses = courses.filter(c =>
        !allQuestions.some(q => q.course && q.course.toLowerCase().includes(c.toLowerCase()))
      );

      let userPrompt;

      if (isStudent) {
        userPrompt = retryCount === 0
          ? `Create ${targetCount} high-quality digital literacy assessment questions for students at the ${difficultyLevel} level.

             IMPORTANT: Use clear, simple language appropriate for students learning computer skills.
             Focus on practical, everyday computer tasks and academic applications.

             Question topics should cover these skill areas: ${courses.join(', ')}.

             Make questions educational and confidence-building:
             - Focus on fundamental computer operations
             - Include basic Microsoft Office applications for schoolwork
             - Cover digital safety and responsible technology use
             - Use scenarios relevant to student life and learning

             Ensure questions are appropriate for the ${difficultyLevel} level of digital literacy.`
          : `Create ${targetCount} additional student-focused assessment questions for these specific skill areas:
             ${missingCourses.join(', ')}.

             IMPORTANT: Keep language simple and educational. Focus on practical skills students need for academic success.
             Make questions clear and confidence-building for learners developing digital literacy.`;
      } else {
        userPrompt = retryCount === 0
          ? `Create ${targetCount} high-quality assessment questions for a ${role} at the ${difficultyLevel} level.

             CRUCIAL: Use VARIED question formats. DO NOT begin every question with "${role}..." or similar patterns.
             Instead, focus directly on the tasks, tools, or problems. For example:
             - "Which Excel feature should be used to..."
             - "When creating a SharePoint site, what permission setting..."
             - "To protect sensitive data in an email attachment, which method..."

             Make questions as precise as needed - there is no length restriction. Include enough context for the scenario to be clear.

             Focus on testing skills from these areas: ${courses.join(', ')}.`
          : `Create ${targetCount} additional assessment questions focusing specifically on these skill areas:
             ${missingCourses.join(', ')}.

             IMPORTANT: Review and vary your question formats! DO NOT start questions with "${role}..."
             Instead, vary your approach and focus on the tasks/problems directly.

             Make questions as detailed as necessary to present clear scenarios.`;
      }

      const completion = await openai.chat.completions.create({
        model: "o3-mini",
        messages: [
          {
            role: "system",
            content: diversePrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        tools: [generate_quiz_questions],
        tool_choice: { type: "function", function: { name: "generate_quiz_questions" } },
        max_completion_tokens: Math.min(maxResponseTokens, availableTokens),
        reasoning_effort: "medium"
      });

      try {
        const message = completion.choices[0].message;

        if (message.tool_calls && message.tool_calls.length > 0 &&
            message.tool_calls[0].function.name === "generate_quiz_questions") {

          const newQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

          // Process and normalize questions
          newQuestions.forEach(q => {
            // Normalize course names if needed
            q.course = normalizeCourse(q.course, courses);

            // Convert options to simple array format if they came in with A, B, C, D format
            if (Array.isArray(q.options) && q.options.length === 4) {
              // If options start with A., B., etc., remove those prefixes
              q.options = q.options.map(opt =>
                opt.replace(/^[A-D]\.\s*/, '').replace(/^[A-D]\)\s*/, '')
              );

              // If answer has A., B., etc. prefix, convert to the actual answer text
              if (/^[A-D]\.?$/.test(q.answer)) {
                const index = q.answer.toUpperCase().charCodeAt(0) - 65; // Convert A->0, B->1, etc.
                if (index >= 0 && index < q.options.length) {
                  q.answer = q.options[index];
                }
              }
            }

            // Basic validation (minimal to ensure question works)
            if (!q.options || q.options.length !== 4) {
              q.options = ["Option A", "Option B", "Option C", "Option D"];
              q.answer = "Option A";
            }

            // Make sure answer is one of the options
            if (!q.options.includes(q.answer)) {
              q.answer = q.options[0];
            }
          });

          // Add new questions, avoiding exact duplicates
          newQuestions.forEach(q => {
            if (!allQuestions.some(existing => existing.question === q.question)) {
              allQuestions.push(q);
            }
          });

          // Trim to 10 questions if we have more, ensuring balanced distribution
          if (allQuestions.length > 10) {
            allQuestions = balanceQuestionsAcrossCourses(allQuestions, targetDistribution).slice(0, 10);
          }
        }
      } catch (parseError) {
        console.warn('Error parsing questions:', parseError);
      }

      retryCount++;
    }

    // If we still don't have enough questions, generate backup questions
    if (allQuestions.length < 10) {
      console.log('Generating backup questions to reach 10 total');
      const remainingQuestions = await generateDiverseBackupQuestions(
        10 - allQuestions.length,
        section,
        role,
        exampleQuestions,
        diversePrompt,
        isStudent
      );
      allQuestions.push(...remainingQuestions);
    }

    // Final format cleanup
    allQuestions = allQuestions.map(q => ({
      question: q.question,
      options: q.options,
      answer: q.answer,
      course: q.course
    })).slice(0, 10);

    // Cache the questions if we have a full set
    if (allQuestions.length === 10) {
      try {
        const cacheKey = `${role}_${section}_questions`;
        await firestore.collection('questionCache').doc(cacheKey).set({
          questions: allQuestions,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('Successfully cached questions for:', { role, section });
      } catch (cacheError) {
        console.warn('Failed to cache questions:', cacheError);
      }
    }

    // Track this request to prevent duplicates
    if (email) {
      trackRequest(email, 'generate-quiz', { role, section });
    }

    res.json(allQuestions);

  } catch (error) {
    console.error('Error generating quiz:', error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-quiz', { role, section });
    }
    res.status(500).json({
      error: 'Failed to generate quiz questions',
      details: error.message
    });
  }
});

// Helper function for extracting student-specific courses for a section
function extractStudentCoursesForSection(section, learningPathData) {
  console.log('Extracting student courses for section:', section);

  // Define student-specific course mappings based on the computer skills classification
  const studentCoursesBySection = {
    'essentials': [
      'Computer Skills – Beginners',
      'Computer Skills – Beginners Plus'
    ],
    'intermediate': [
      'Computer Skills – Improvers Plus',
      'Computer Skills for Everyday Life – Level 1'
    ],
    'advanced': [
      'ICDL Level 2',
      'Computer Skills for Work – Level 2'
    ],
    'champions': [
      'ICDL Level 3'
    ]
  };

  const sectionKey = section.toLowerCase();
  const targetCourses = studentCoursesBySection[sectionKey] || studentCoursesBySection['essentials'];

  // Find matching courses in the learning path data
  const matchedCourses = [];

  targetCourses.forEach(targetCourse => {
    // Look for exact matches or partial matches in the learning path data
    const matches = learningPathData.allCourses.filter(course => {
      const courseTitle = course.title.toLowerCase();
      const target = targetCourse.toLowerCase();

      // Check for exact match or if the course title contains the target
      return courseTitle === target ||
             courseTitle.includes(target) ||
             target.includes(courseTitle.split(' - ')[0].toLowerCase());
    });

    if (matches.length > 0) {
      // Prefer courses from the appropriate pathway level
      const pathwayPriority = {
        'essentials': 'Essentials Learning Pathway',
        'intermediate': 'Intermediate Learning Pathway',
        'advanced': 'Advanced Learning Pathway',
        'champions': 'Champions Learning Pathway'
      };

      const preferredPathway = pathwayPriority[sectionKey];
      const preferredMatch = matches.find(m => m.pathway === preferredPathway);

      if (preferredMatch) {
        matchedCourses.push(preferredMatch.title);
      } else {
        matchedCourses.push(matches[0].title);
      }
    } else {
      // If no exact match, add the target course name for question generation
      matchedCourses.push(targetCourse);
    }
  });

  console.log('Student courses found for section', section, ':', matchedCourses);
  return matchedCourses;
}

// Helper function for extracting relevant courses for a role and section
function extractRelevantCoursesForRoleAndSection(role, section, framework, learningPathData) {
  const relevantCourses = new Set();
  const competencyKeywords = new Set();
  const roleKeywords = role.toLowerCase().split(/\s+/);

  // Map section to pathway
  let targetPathway;
  switch(section.toLowerCase()) {
    case 'essentials':
    case 'foundational':
      targetPathway = 'Essentials Learning Pathway';
      break;
    case 'intermediate':
      targetPathway = 'Intermediate Learning Pathway';
      break;
    case 'advanced':
      targetPathway = 'Advanced Learning Pathway';
      break;
    case 'champions':
    case 'expert':
      targetPathway = 'Champions Learning Pathway';
      break;
    default:
      targetPathway = 'Essentials Learning Pathway';
  }

  try {
    // First, get all courses from the target pathway
    const pathwayCourses = learningPathData.coursesByPathway[targetPathway] || [];
    pathwayCourses.forEach(course => relevantCourses.add(course.title));

    // Next, extract courses from the framework that are relevant to the role
    if (framework?.coreCompetencies) {
      framework.coreCompetencies.forEach(competency => {
        // Extract competency title as a keyword
        competencyKeywords.add(competency.title.toLowerCase());

        if (competency.requiredSkills) {
          competency.requiredSkills.forEach(skill => {
            // Extract course names from skills (typically in format "Course Name - Explanation")
            const courseMatch = skill.match(/^([^-]+)\s*-/);
            if (courseMatch && courseMatch[1]) {
              const courseName = courseMatch[1].trim();

              // Find exact or similar course in the learning path data
              const exactMatches = learningPathData.allCourses.filter(c =>
                c.title === courseName || c.title.includes(courseName)
              );

              exactMatches.forEach(match => {
                if (match.pathway === targetPathway) {
                  relevantCourses.add(match.title);
                }
              });
            }

            // Extract subtopics from required skills
            const skillParts = skill.split('-');
            if (skillParts.length > 1) {
              competencyKeywords.add(skillParts[1].trim().toLowerCase());
            }
          });
        }
      });
    }
  } catch (error) {
    console.warn('Error extracting relevant courses:', error);
  }

  return {
    courses: Array.from(relevantCourses),
    competencyKeywords: Array.from(competencyKeywords),
    roleKeywords
  };
}

// Normalize course name to match one of the target courses
function normalizeCourse(courseName, targetCourses) {
  if (!courseName) return 'General';

  // Check for direct matches or substrings
  for (const target of targetCourses) {
    if (courseName.toLowerCase().includes(target.toLowerCase())) {
      return target;
    }
  }

  // Look for partial matches
  const words = courseName.toLowerCase().split(/\W+/);
  for (const target of targetCourses) {
    const targetWords = target.toLowerCase().split(/\W+/);
    if (targetWords.some(tw => words.includes(tw))) {
      return target;
    }
  }

  return courseName;
}

// Balance questions across courses more effectively
function balanceQuestionsAcrossCourses(questions, targetDistribution) {
  // Group questions by course
  const courseGroups = {};
  questions.forEach(q => {
    const course = q.course || 'General';
    courseGroups[course] = courseGroups[course] || [];
    courseGroups[course].push(q);
  });

  // Determine how many questions we want from each course
  const coursesNeeded = Object.keys(targetDistribution);
  const idealCount = Math.min(10, questions.length) / coursesNeeded.length;

  // For each course, keep the best N questions (where N is based on target distribution)
  const balanced = [];

  // First pass: take the minimum number from each course to ensure coverage
  for (const course of coursesNeeded) {
    const groupQuestions = courseGroups[course] || [];
    const targetCount = Math.ceil(targetDistribution[course] || idealCount);
    const takeCount = Math.min(groupQuestions.length, targetCount);

    if (takeCount > 0) {
      balanced.push(...groupQuestions.slice(0, takeCount));
      courseGroups[course] = groupQuestions.slice(takeCount);
    }
  }

  // Second pass: fill remaining slots with leftover questions
  const remaining = 10 - balanced.length;
  if (remaining > 0) {
    const leftover = Object.values(courseGroups).flat();
    balanced.push(...leftover.slice(0, remaining));
  }

  return balanced;
}

// Generate backup questions with diverse formats
async function generateDiverseBackupQuestions(count, section, role, _, diversePrompt, isStudent = false) {
  try {
    // Define the generate_quiz_questions function
    const generate_quiz_questions = {
      type: "function",
      function: {
        name: "generate_quiz_questions",
        description: "Generate diverse format assessment questions",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: { type: "string" },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 4,
                    maxItems: 4
                  },
                  answer: { type: "string" },
                  course: { type: "string" }
                },
                required: ["question", "options", "answer", "course"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    let userPrompt;
    if (isStudent) {
      userPrompt = `Create ${count} digital literacy assessment questions for students at the ${section} level.

        IMPORTANT: Use clear, simple language appropriate for students learning computer skills.
        Focus on fundamental computer operations, basic Microsoft Office applications, and digital safety.

        Each question should:
        - Use straightforward, easy-to-understand language
        - Focus on practical, everyday computer tasks
        - Be relevant to academic and personal use
        - Have 4 answer options with one clearly correct answer
        - Test basic digital literacy skills appropriate for the ${section} level

        Question formats should vary and include:
        - "What should you do when..."
        - "Which option is used to..."
        - "How do you..."
        - "What happens when you..."`;
    } else {
      userPrompt = `Create ${count} assessment questions focusing on essential Microsoft 365 and cybersecurity skills.

        CRUCIAL INSTRUCTION: Use VARIED question formats. DO NOT begin questions with "${role}..."
        Instead, focus on the tasks, tools, or problems directly with questions like:
        - "What is the correct method to..."
        - "Which setting enables..."
        - "To resolve [specific problem], what action should be taken?"

        Make questions as precise and detailed as needed - there is no length restriction.

        Each question should:
        - Have 4 answer options with only one correct answer
        - Test practical knowledge applicable to the ${section} level
        - Be clear and focused on specific skills`;
    }

    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: [
        {
          role: "system",
          content: diversePrompt || `Create diverse format assessment questions that vary in structure and don't repeatedly mention the role.`
        },
        {
          role: "user",
          content: userPrompt
        }
      ],
      tools: [generate_quiz_questions],
      tool_choice: { type: "function", function: { name: "generate_quiz_questions" } },
      max_completion_tokens: 5000,
      reasoning_effort: "high" // Using high reasoning for best quality
    });

    const message = completion.choices[0].message;

    if (message.tool_calls && message.tool_calls.length > 0 &&
        message.tool_calls[0].function.name === "generate_quiz_questions") {

      const backupQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

      // Process questions without strict validation
      backupQuestions.forEach(q => {
        // Normalize options if needed
        if (Array.isArray(q.options) && q.options.length === 4) {
          q.options = q.options.map(opt =>
            opt.replace(/^[A-D]\.\s*/, '').replace(/^[A-D]\)\s*/, '')
          );

          if (/^[A-D]\.?$/.test(q.answer)) {
            const index = q.answer.toUpperCase().charCodeAt(0) - 65;
            if (index >= 0 && index < q.options.length) {
              q.answer = q.options[index];
            }
          }
        }

        // Basic validation fixes
        if (!q.options || q.options.length !== 4) {
          q.options = ["Option A", "Option B", "Option C", "Option D"];
          q.answer = "Option A";
        }

        // Make sure answer is one of the options
        if (!q.options.includes(q.answer)) {
          q.answer = q.options[0];
        }

        // Ensure course is set
        if (!q.course) {
          q.course = 'General';
        }
      });

      return backupQuestions.slice(0, count);
    }

    return [];
  } catch (error) {
    console.warn('Error generating backup questions:', error);
    return [];
  }
}


function calculateTargetDistribution(courses, totalQuestions = 10) {
  const distribution = {};
  try {
    const baseQuestionsPerCourse = Math.floor(totalQuestions / courses.length);
    const remainingQuestions = totalQuestions % courses.length;

    courses.forEach((course, index) => {
      distribution[course] = baseQuestionsPerCourse;
      if (index < remainingQuestions) {
        distribution[course]++;
      }
    });
  } catch (error) {
    console.warn('Error calculating distribution:', error);
    courses.forEach(course => {
      distribution[course] = Math.floor(totalQuestions / courses.length);
    });
  }
  return distribution;
}


// Function to generate student-focused framework
function getStudentFramework() {
  return {
    role: {
      title: "Student - Digital Skills Development",
      description: "A comprehensive framework for students to develop essential digital literacy and Microsoft 365 skills for academic success and future career preparation."
    },
    coreCompetencies: [
      {
        id: "fundamental-computer-operations",
        title: "Fundamental Computer Operations",
        requiredSkills: [
          "Computer Skills – Beginners - Essentials - Learn fundamental computer operations including switching on/off, logging in, keyboard and mouse usage, and safely opening/closing programs",
          "Computer Skills – Beginners Plus - Essentials - Build on basic skills with text document creation, internet usage, email setup, and mobile technology basics"
        ]
      },
      {
        id: "enhanced-computer-skills",
        title: "Enhanced Computer Skills",
        requiredSkills: [
          "Computer Skills – Improvers Plus - Intermediate - Enhance software skills including file management, online searching, and digital safety",
          "Computer Skills for Everyday Life – Level 1 - Intermediate - Improve skills using Microsoft applications, understand websites, and internet banking"
        ]
      },
      {
        id: "professional-computer-skills",
        title: "Professional Computer Skills",
        requiredSkills: [
          "ICDL Level 2 - Advanced - Enhance competence in word processing, spreadsheet, and presentation software for office roles",
          "Computer Skills for Work – Level 2 - Advanced - Focus on workplace-specific word processing, spreadsheet, and presentation skills"
        ]
      },
      {
        id: "advanced-computer-skills",
        title: "Advanced Computer Skills",
        requiredSkills: [
          "ICDL Level 3 - Champions - Advanced Microsoft applications knowledge for further education and IT careers"
        ]
      }
    ],
    developmentPath: {
      levels: [
        {
          name: "Essentials",
          focus: "Foundation Skills",
          outcomes: [
            "Master basic computer operations and navigation",
            "Create and edit simple documents, spreadsheets, and presentations",
            "Use email and basic online communication tools effectively",
            "Understand fundamental digital safety practices"
          ]
        },
        {
          name: "Intermediate",
          focus: "Academic Application",
          outcomes: [
            "Apply Microsoft Office skills to academic projects and assignments",
            "Collaborate effectively using digital tools and platforms",
            "Organize and manage digital files and resources efficiently",
            "Demonstrate responsible digital citizenship"
          ]
        },
        {
          name: "Advanced",
          focus: "Professional Preparation",
          outcomes: [
            "Use advanced features in Microsoft Office applications",
            "Create professional-quality documents and presentations",
            "Understand workplace digital communication standards",
            "Apply advanced cybersecurity practices"
          ]
        },
        {
          name: "Champions",
          focus: "Digital Leadership",
          outcomes: [
            "Mentor others in digital skills development",
            "Innovate with advanced digital tools and technologies",
            "Lead digital projects and initiatives",
            "Prepare for technology-focused career paths"
          ]
        }
      ]
    },
    successMetrics: [
      "Successful completion of basic computer skills assessments",
      "Ability to create academic documents using Microsoft Office",
      "Effective participation in digital collaboration activities",
      "Demonstration of safe and responsible digital practices",
      "Progression through learning pathway levels",
      "Application of digital skills in academic and personal contexts"
    ]
  };
}

function extractCoursesFromFramework(framework) {
  const courses = new Set();
  try {
    if (Array.isArray(framework.coreCompetencies)) {
      framework.coreCompetencies.forEach(competency => {
        if (Array.isArray(competency.requiredSkills)) {
          competency.requiredSkills.forEach(skill => {
            const courseName = skill.split('-')[0].trim();
            if (courseName) courses.add(courseName);
          });
        }
      });
    }
  } catch (error) {
    console.warn('Error extracting courses:', error);
  }
  return Array.from(courses);
}

function getDifficultyLevel(section) {
  switch (section.toLowerCase()) {
    case 'foundational':
      return 'moderately challenging - focus on practical application of fundamental concepts with carefully crafted distractors. Questions should test understanding rather than mere recall, incorporating realistic scenarios and plausible alternative options.';
    case 'intermediate':
      return 'challenging - present complex scenarios that require combining multiple concepts. Include subtle distinctions between options and test deeper understanding of feature interactions and best practices.';
    case 'advanced':
      return 'very challenging - focus on sophisticated problem-solving, edge cases, and strategic decision-making. Questions should require deep understanding of advanced features, system interactions, and optimization considerations.';
    case 'expert':
      return 'highly challenging - test mastery through complex scenarios involving multiple interconnected concepts, strategic planning, and innovative problem-solving. Questions should require comprehensive understanding and the ability to evaluate trade-offs between different approaches.';
    default:
      return 'challenging - include realistic scenarios that test practical application and understanding';
  }
}

async function getCachedQuestions(role, section) {
  try {
    const cacheKey = `${role}_${section}_questions`;
    const cached = await firestore.collection('questionCache').doc(cacheKey).get();

    if (cached.exists) {
      const data = cached.data();
      const cacheAge = Date.now() - data.timestamp.toDate();
      const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      if (cacheAge < CACHE_TTL) {
        console.log(`Using cached questions for ${role}/${section}, cache age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
        return data.questions;
      } else {
        console.log(`Cache expired for ${role}/${section}, age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
      }
    }
    return null;
  } catch (error) {
    console.warn('Cache retrieval failed:', error);
    return null;
  }
}


// Add this new endpoint to server.js
app.post('/api/generate-self-assessment', async (req, res) => {
  console.log('Received self-assessment generation request:', {
    role: req.body.role,
    section: req.body.section,
    email: req.body.email // Log email for debugging
  });

  const { role, section, framework, email } = req.body;

  if (!role || !section || !framework) {
    console.log('Missing required fields:', { role, section, framework: !!framework });
    return res.status(400).json({
      error: 'Missing required fields',
      details: {
        role: !role ? 'Missing role' : undefined,
        section: !section ? 'Missing section' : undefined,
        framework: !framework ? 'Missing framework' : undefined
      }
    });
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-self-assessment', { role, section })) {
    console.log(`Duplicate self-assessment request detected for ${email}`);

    // Try to return cached questions
    const cachedQuestions = await getCachedSelfAssessmentQuestions(role, section);
    if (cachedQuestions?.length === 5) {
      return res.json(cachedQuestions);
    }
  }

  try {
    // Check cache first
    const cachedQuestions = await getCachedSelfAssessmentQuestions(role, section);
    if (cachedQuestions?.length === 5) {
      console.log('Retrieved self-assessment questions from cache for:', { role, section });
      return res.json(cachedQuestions);
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for self-assessment, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    console.log('No cached self-assessment questions found, generating new questions');

    // Define the generate_self_assessment_questions function for o3-mini
    const generate_self_assessment_questions = {
      type: "function",
      function: {
        name: "generate_self_assessment_questions",
        description: "Generate role-specific self-assessment questions for skill evaluation",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: {
                    type: "string",
                    description: "A role-specific self-assessment question asking about user's skill level with a scenario relevant to their job role"
                  },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 3,
                    maxItems: 3,
                    description: "Three options representing increasing levels of skill/proficiency from basic to advanced"
                  },
                  skillArea: {
                    type: "string",
                    description: "The specific skill area being assessed, matching one of the course topics"
                  },
                  type: {
                    type: "string",
                    enum: ["self-assessment"],
                    description: "The type of question (always 'self-assessment')"
                  }
                },
                required: ["question", "options", "skillArea", "type"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    // Extract relevant courses and competency keywords for the role and section
    const extractedData = extractRelevantCoursesForRoleAndSection(role, section, framework, globalLearningPathData);

    // Extract courses directly from the framework as intended
    const courses = extractCoursesFromFramework(framework);
    const competencyKeywords = extractedData.competencyKeywords || [];
    const roleKeywords = extractedData.roleKeywords || [];

    // Calculate tokens for API call
    const { encode } = require('gpt-tokenizer');
    const messages = [
      {
        role: "system",
        content: `You are an expert assessment designer specializing in Microsoft 365 and cybersecurity self-assessments. Create questions that help users evaluate their own skill levels.`
      },
      {
        role: "user",
        content: `Create 5 high-quality self-assessment questions for a ${role} at the ${section} level.

IMPORTANT CONTEXT:
- These questions are for a ${role} who needs specific skills from these courses: ${courses.join(', ')}
- The assessment is measuring their current level in the ${section} learning pathway
- Questions should reflect realistic tasks this role would perform using these technologies
- Key competency areas for this role include: ${competencyKeywords.join(', ')}

For each question:
1. Focus on tool proficiency and practical usage scenarios rather than starting with role references
2. Ask about their comfort/skill level with a specific task or tool feature
3. Provide exactly 3 answer options representing increasing competency levels:
   - Option 1: Basic/beginner level skill (minimal capability)
   - Option 2: Intermediate level skill (functional capability)
   - Option 3: Advanced/expert level skill (optimal capability)

PREFERRED FORMATS:
- "How do you handle data visualization in Excel when working with financial reports?"
- "What approach do you take when securing sensitive documents in SharePoint?"
- "When collaborating on a presentation, which PowerPoint features do you utilize?"

FOCUS AREAS:
- Design questions focused on tool proficiency and practical usage scenarios
- Questions should assess HOW the user works with specific tools and features
- Skills specifically needed from these courses: ${courses.join(', ')}
- Tasks typically performed using Microsoft 365 tools
- Productivity challenges they might face

Remember to vary the question formats while maintaining relevance to the ${role} position.`
      }
    ];

    const promptTokens = messages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 100000; // o3-mini's max output
    const availableTokens = contextWindow - promptTokens;

    const max_tokens = Math.min(maxResponseTokens, availableTokens);

    if (max_tokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: messages,
      tools: [generate_self_assessment_questions],
      tool_choice: { type: "function", function: { name: "generate_self_assessment_questions" } },
      max_completion_tokens: max_tokens,
      reasoning_effort: "low"
    });

    let selfAssessmentQuestions = [];

    try {
      const message = completion.choices[0].message;

      if (message.tool_calls && message.tool_calls.length > 0 &&
          message.tool_calls[0].function.name === "generate_self_assessment_questions") {

        selfAssessmentQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

        // Validation and question improvement without forcing role references
        selfAssessmentQuestions = selfAssessmentQuestions.map(q => {
          // Keep the original question without forcing role references
          const question = q.question;

          // Ensure skill area is set appropriately
          const skillArea = q.skillArea || normalizeCourse(q.skillArea || 'General', courses);

          // Ensure options represent increasing skill levels
          let options = q.options.slice(0, 3); // Ensure exactly 3 options

          // Sort options by length as a proxy for complexity - longer answers tend to be more detailed
          if (options.length === 3 && options[0].length > options[2].length) {
            options = [...options].sort((a, b) => a.length - b.length);
          }

          return {
            question: question,
            options: options,
            skillArea: skillArea,
            type: "self-assessment" // Ensure type is set
          };
        });
      }
    } catch (parseError) {
      console.warn('Error parsing self-assessment questions:', parseError);
      return res.status(500).json({
        error: 'Failed to generate self-assessment questions',
        details: parseError.message
      });
    }

    // Trim to exactly 5 questions
    selfAssessmentQuestions = selfAssessmentQuestions.slice(0, 5);

    // Cache the questions if we have a full set with enhanced metadata
    if (selfAssessmentQuestions.length === 5) {
      try {
        const cacheKey = `${role}_${section}_self_assessment`;
        await firestore.collection('questionCache').doc(cacheKey).set({
          questions: selfAssessmentQuestions,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          // Add these fields to improve future generation
          role: role,
          roleKeywords: roleKeywords,
          competencyKeywords: competencyKeywords,
          courses: courses,
          section: section
        });
        console.log('Successfully cached self-assessment questions for:', { role, section });
      } catch (cacheError) {
        console.warn('Failed to cache self-assessment questions:', cacheError);
      }
    }

    // Track this request to prevent duplicates
    if (email) {
      trackRequest(email, 'generate-self-assessment', { role, section });
    }

    res.json(selfAssessmentQuestions);

  } catch (error) {
    console.error('Error generating self-assessment questions:', error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-self-assessment', { role, section });
    }
    res.status(500).json({
      error: 'Failed to generate self-assessment questions',
      details: error.message
    });
  }
});

// Add helper function for caching self-assessment questions
async function getCachedSelfAssessmentQuestions(role, section) {
  try {
    const cacheKey = `${role}_${section}_self_assessment`;
    const cached = await firestore.collection('questionCache').doc(cacheKey).get();

    if (cached.exists) {
      const data = cached.data();
      const cacheAge = Date.now() - data.timestamp.toDate();
      const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      if (cacheAge < CACHE_TTL) {
        console.log(`Using cached self-assessment questions for ${role}/${section}, cache age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
        return data.questions;
      } else {
        console.log(`Cache expired for ${role}/${section} self-assessment, age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
      }
    }
    return null;
  } catch (error) {
    console.warn('Self-assessment cache retrieval failed:', error);
    return null;
  }
}


// Add proper error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION:', error);
  // Log to a monitoring service if available
});

async function analyzeEnglishProficiencyWithAI(response, studentLevel, preliminaryResponses = []) {
  try {
    console.log('Sending English response to AI for analysis...');

    const analysisPrompt = `You are an expert English language assessor specialising in evaluating written English proficiency for educational placement. Your task is to analyse a student's written response and provide a numerical score out of 21 points based on English proficiency levels.

CRITICAL: Use UK English spelling, grammar, and vocabulary conventions throughout your analysis and feedback (e.g., "colour" not "color", "realise" not "realize", "organised" not "organized", "centre" not "center", "behaviour" not "behavior", "analyse" not "analyze", "recognise" not "recognize", "specialise" not "specialize").

SCORING CRITERIA (Total: 21 points):

1. GRAMMAR & SYNTAX (7 points):
   - Sentence structure and complexity
   - Correct use of tenses
   - Subject-verb agreement
   - Proper use of articles, prepositions

2. VOCABULARY & EXPRESSION (7 points):
   - Range and appropriateness of vocabulary
   - Word choice accuracy
   - Ability to express ideas clearly
   - Use of descriptive language

3. COHERENCE & ORGANISATION (7 points):
   - Logical flow of ideas
   - Use of connecting words/phrases
   - Overall structure and organisation
   - Clarity of communication

PROFICIENCY LEVELS:
- 16-21 points: L2/GCSE level (Advanced - proceed to digital skills)
- 10-15 points: L1 level (Intermediate - English training recommended)
- 0-9 points: Entry level (Beginner - English training required)

ASSESSMENT TASK: "Describe which season you like best"

${preliminaryResponses.length > 0 ? `
PRELIMINARY ASSESSMENT DATA:
The student completed ${preliminaryResponses.length} preliminary questions before the writing task:

${preliminaryResponses.map((resp, index) => {
  const q = resp.question;
  const r = resp.response;

  switch (q.type) {
    case 'grammar':
      return `${index + 1}. Grammar Question: "${q.content}"
   Student correction: "${r.correctedSentence}"
   Expected answer: "${q.correctAnswer}"`;

    case 'vocabulary':
      return `${index + 1}. Vocabulary Question: "${q.content}"
   Student selected: "${r.selectedAnswer}"
   Correct answer: "${q.correctAnswer}"`;

    case 'sentence':
      return `${index + 1}. Sentence Ordering: [${q.content.join(', ')}]
   Student constructed: "${r.constructedSentence}"
   Correct answer: "${q.correctAnswer}"`;

    default:
      return `${index + 1}. Question type: ${q.type}`;
  }
}).join('\n\n')}

Use this preliminary data to inform your assessment of the student's overall English proficiency across multiple skills (grammar, vocabulary, sentence structure). Note: Spelling mistakes in the essay should be considered naturally as part of the assessment - students are not required to correct spelling errors.
` : ''}

IMPORTANT ASSESSMENT GUIDELINES:
- This is a natural assessment where students provide unguided responses
- Incorrect grammar, spelling, or vocabulary in preliminary questions indicates lower proficiency
- Very short responses (under 50 words) typically indicate Entry Level proficiency
- Empty or minimal responses should receive scores of 0-9 (Entry Level)
- Students who struggle with basic sentence construction should score 0-9 (Entry Level)
- Consider response length, complexity, and accuracy together when determining proficiency level

CRITICAL SCORING GUIDELINES (MUST BE FOLLOWED EXACTLY):
- 16-21 points: Level 2/GCSE (good responses with minor errors, strong proficiency)
- 10-15 points: Level 1 (adequate responses with some errors, moderate proficiency)
- 0-9 points: Entry Level (poor responses, basic errors, limited proficiency)

Please analyse the following student response and provide:
1. A numerical score out of 21 (following the guidelines above)
2. The proficiency level
3. Brief feedback on strengths and areas for improvement using UK English
${preliminaryResponses.length > 0 ? '4. Consider both the preliminary questions performance and the written response' : ''}

Student Level: ${studentLevel || 'Not specified'}
Student Response: "${response}"

Provide your analysis in the following JSON format using UK English spelling and vocabulary throughout:
{
  "score": [number 0-21],
  "level": "[Entry/L1/L2/GCSE]",
  "feedback": {
    "grammar": "[brief assessment of grammar skills using UK English]",
    "vocabulary": "[brief assessment of vocabulary usage using UK English]",
    "coherence": "[brief assessment of organisation and clarity using UK English]",
    "overall": "[overall summary and recommendations using UK English]"
  },
  "strengths": ["strength1 using UK English", "strength2 using UK English"],
  "improvements": ["area1 using UK English", "area2 using UK English"]
}`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert English language assessor conducting a natural proficiency assessment. Use UK English spelling, grammar, and vocabulary conventions throughout your analysis and feedback. Students provide unguided responses without correction prompts. Short, incorrect, or minimal responses are valid indicators of lower English proficiency levels. Score accurately based on actual demonstrated ability, including Entry Level 2 (0-5 points) for very poor responses and Entry Level 3 (6-9 points) for basic responses with significant errors."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.3
    });

    const analysisText = completion.choices[0].message.content;
    console.log('Raw AI analysis response:', analysisText);

    // Parse the JSON response
    let analysisResult;
    try {
      // Extract JSON from the response
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in AI response');
      }
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      // Fallback analysis based on response length and basic criteria
      analysisResult = generateFallbackAnalysis(response);
    }

    // Validate and normalize the score
    analysisResult.score = Math.max(0, Math.min(21, parseInt(analysisResult.score) || 0));

    // Determine level based on score (CORRECTED SCORING SYSTEM)
    if (analysisResult.score >= 16) {
      analysisResult.level = 'L2/GCSE';
    } else if (analysisResult.score >= 10) {
      analysisResult.level = 'L1';
    } else {
      analysisResult.level = 'Entry Level';
    }

    // Add course recommendations based on English proficiency level
    analysisResult.courseRecommendations = generateCourseRecommendations(analysisResult.score, analysisResult.level);

    console.log('Final English proficiency analysis:', {
      score: analysisResult.score,
      level: analysisResult.level,
      recommendedCourses: analysisResult.courseRecommendations.eligible.length
    });

    return analysisResult;

  } catch (error) {
    console.error('Error in AI English proficiency analysis:', error);

    // Return fallback analysis in case of AI failure
    return generateFallbackAnalysis(response);
  }
}

// Generate course recommendations based on English proficiency level
function generateCourseRecommendations(score, level) {
  let recommendations = {
    eligible: [],
    description: '',
    nextSteps: ''
  };

  if (score >= 16) {
    // Level 2 English Achievement (L2/GCSE)
    recommendations.eligible = [
      'Level 3 Digital Skills Course',
      'Level 3 Health and Social Care Course',
      'Level 2 Digital Skills Course (and below)',
      'Level 2 Health and Social Care Course'
    ];
    recommendations.description = 'Congratulations! You have achieved Level 2 English proficiency. You are eligible for our most advanced courses.';
    recommendations.nextSteps = 'You can proceed directly to Level 3 courses or choose from any lower level courses based on your interests and career goals.';
  } else if (score >= 10) {
    // Level 1 English Assessment
    recommendations.eligible = [
      'Level 2 Health and Social Care Course',
      'Level 2 Digital Skills Course (and below)',
      'Level 2 English Course',
      'Level 1 Digital Skills Courses'
    ];
    recommendations.description = 'You have achieved Level 1 English proficiency. You have access to a good range of Level 2 courses.';
    recommendations.nextSteps = 'Consider taking Level 2 courses to advance your skills, or take a Level 2 English course to work toward Level 2 English proficiency.';
  } else {
    // Entry Level Assessment (9 points or below)
    recommendations.eligible = [
      'Beginners Digital Skills Course',
      'Beginners Plus Digital Skills Course',
      'Level 1 English Course',
      'Entry Level Health & Social Care Courses',
      'Basic English Support Courses'
    ];
    recommendations.description = 'You are at Entry Level English proficiency. We recommend starting with our foundational courses.';
    recommendations.nextSteps = 'Begin with beginner courses and English support to build your foundation, then progress to Level 1 English to unlock more advanced opportunities.';
  }

  return recommendations;
}

function generateFallbackAnalysis(response) {
  console.log('Generating fallback English proficiency analysis');

  const wordCount = response.trim().split(/\s+/).length;
  const sentenceCount = response.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const avgWordsPerSentence = wordCount / Math.max(sentenceCount, 1);

  // Natural assessment scoring - account for very short/poor responses
  let score = 0;

  // Length scoring (0-7 points) - more sensitive to short responses
  if (wordCount >= 150) score += 7;
  else if (wordCount >= 100) score += 5;
  else if (wordCount >= 50) score += 3;
  else if (wordCount >= 20) score += 2;
  else if (wordCount >= 5) score += 1;
  else score += 0; // Very short or empty responses get 0 points

  // Sentence complexity (0-7 points) - more sensitive to poor structure
  if (avgWordsPerSentence >= 12) score += 7;
  else if (avgWordsPerSentence >= 8) score += 5;
  else if (avgWordsPerSentence >= 5) score += 3;
  else if (avgWordsPerSentence >= 2) score += 1;
  else score += 0; // Very poor sentence structure gets 0 points

  // Basic structure (0-7 points) - account for minimal responses
  const hasVariedSentences = sentenceCount >= 3;
  const hasDescriptiveWords = /\b(beautiful|wonderful|amazing|lovely|favorite|enjoy|because|however|although)\b/i.test(response);
  const hasBasicStructure = sentenceCount >= 1 && wordCount >= 10;

  if (hasVariedSentences && hasDescriptiveWords) score += 7;
  else if (hasVariedSentences || hasDescriptiveWords) score += 4;
  else if (hasBasicStructure) score += 2;
  else score += 0; // No coherent structure gets 0 points

  // Cap the score at 21
  score = Math.min(21, score);

  // Corrected level determination following the required scoring system
  let level;
  if (score >= 16) level = 'L2/GCSE';
  else if (score >= 10) level = 'L1';
  else level = 'Entry Level'; // 9 points or below = Entry Level

  // Provide appropriate feedback based on actual performance (corrected scoring)
  let strengths = [];
  let improvements = [];
  let feedbackText = '';

  if (score <= 9) {
    // Entry Level (0-9 points)
    if (score <= 3) {
      strengths = wordCount > 0 ? ['Attempted the assessment'] : ['Participated in assessment'];
      improvements = ['Basic English writing practice', 'Vocabulary building', 'Simple sentence construction'];
      feedbackText = `Very short response (${wordCount} words) indicates need for foundational English support`;
    } else {
      strengths = ['Basic communication attempted', 'Some sentence structure present'];
      improvements = ['Grammar practice', 'Expanding vocabulary', 'Writing longer responses'];
      feedbackText = `Response (${wordCount} words) shows basic English skills with significant room for improvement`;
    }
  } else if (score <= 15) {
    // Level 1 (10-15 points)
    strengths = ['Adequate communication skills', 'Basic sentence structure', 'Some vocabulary range'];
    improvements = ['Grammar accuracy', 'Vocabulary expansion', 'Writing organization'];
    feedbackText = `Response (${wordCount} words) demonstrates Level 1 English proficiency with some areas for improvement`;
  } else {
    // Level 2/GCSE (16-21 points)
    strengths = ['Good communication skills', 'Clear sentence structure', 'Appropriate vocabulary'];
    improvements = ['Advanced grammar structures', 'Academic vocabulary', 'Complex writing techniques'];
    feedbackText = `Response (${wordCount} words) demonstrates strong English proficiency at Level 2/GCSE standard`;
  }

  return {
    score: score,
    level: level,
    feedback: {
      grammar: score <= 9 ? (score <= 3 ? 'Needs foundational support' : 'Basic level with significant errors') :
               score <= 15 ? 'Adequate with some errors' : 'Good grammar skills demonstrated',
      vocabulary: score <= 9 ? (score <= 3 ? 'Very limited vocabulary' : 'Limited vocabulary range') :
                  score <= 15 ? 'Adequate vocabulary usage' : 'Good vocabulary range demonstrated',
      coherence: score <= 9 ? (score <= 3 ? 'Minimal structure present' : 'Basic structure with issues') :
                 score <= 15 ? 'Adequate organization' : 'Clear structure and organization',
      overall: feedbackText
    },
    strengths: strengths,
    improvements: improvements,
    courseRecommendations: generateCourseRecommendations(score, level)
  };
}

// Generate Preliminary Questions Endpoint
app.post('/api/generate-preliminary-questions', async (req, res) => {
  console.log('Received preliminary questions generation request');

  try {
    const { studentLevel, questionCount = 8 } = req.body;

    // Validate required fields
    if (!studentLevel) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: { studentLevel: 'required' }
      });
    }

    console.log(`Generating ${questionCount} preliminary questions for level: ${studentLevel}`);

    // Generate questions using AI
    const questions = await generatePreliminaryQuestions(studentLevel, questionCount);

    res.json({
      success: true,
      questions: questions,
      studentLevel: studentLevel,
      questionCount: questions.length
    });

  } catch (error) {
    console.error('Error generating preliminary questions:', error);

    // Return fallback questions on error
    const fallbackQuestions = getFallbackPreliminaryQuestions();

    res.json({
      success: false,
      error: 'AI generation failed, using fallback questions',
      questions: fallbackQuestions,
      fallback: true
    });
  }
});

// Helper function to generate preliminary questions using AI
async function generatePreliminaryQuestions(studentLevel, questionCount) {
  const prompt = `Generate ${questionCount} interactive English assessment questions for ${studentLevel} level students (Entry to L2/GCSE level).

CRITICAL: Use UK English spelling, grammar, and vocabulary conventions throughout all questions, answers, and content (e.g., "colour" not "color", "realise" not "realize", "organised" not "organized", "centre" not "center", "behaviour" not "behavior", "favourite" not "favorite").

Create a mix of question types (NO SPELLING QUESTIONS - natural assessment):
1. Grammar rewriting (rewrite sentence with correct grammar - free text response)
2. Vocabulary selection (multiple choice word usage in context)
3. Sentence construction (use given words to write a sentence - free text response)
4. Drag-and-drop sentence construction (arrange words by dragging - interactive)
5. Interactive grammar correction (click-to-edit or dropdown-based corrections)
6. Vocabulary matching (match words to definitions or contexts)
7. Reading comprehension (short passage with multiple choice questions)

Requirements:
- Questions should be culturally neutral and appropriate for adult learners
- Difficulty should match ${studentLevel} level
- Content should be practical and relevant to everyday situations
- Each question should take 30-60 seconds to complete
- Include a variety of interactive elements to engage students
- Use UK English vocabulary and phrasing throughout
- IMPORTANT: correctAnswer must ALWAYS be a string, never an object or array

Return a JSON array with this exact structure (NO SPELLING QUESTIONS):
[
  {
    "type": "grammar",
    "question": "Rewrite this sentence correctly:",
    "content": "She don't like to goes to the store yesterday.",
    "correctAnswer": "She didn't like to go to the store yesterday.",
    "mistakes": ["don't", "goes"]
  },
  {
    "type": "vocabulary",
    "question": "Choose the best word to complete the sentence:",
    "content": "The weather today is very _____ for a picnic.",
    "options": ["terrible", "perfect", "impossible", "dangerous"],
    "correctAnswer": "perfect"
  },
  {
    "type": "sentence",
    "question": "Use these words to write a complete sentence:",
    "content": ["school", "to", "walks", "every", "day", "she"],
    "correctAnswer": "She walks to school every day."
  },
  {
    "type": "drag-drop",
    "question": "Drag the words to form a correct sentence:",
    "content": ["The", "cat", "is", "sleeping", "on", "the", "sofa"],
    "correctAnswer": "The cat is sleeping on the sofa.",
    "shuffledWords": ["sleeping", "The", "sofa", "cat", "on", "is", "the"]
  },
  {
    "type": "interactive-grammar",
    "question": "Click on the words that need to be corrected:",
    "content": "He don't knows how to swim very good.",
    "correctAnswer": "He doesn't know how to swim very well.",
    "mistakes": [{"word": "don't", "correction": "doesn't", "position": 1}, {"word": "knows", "correction": "know", "position": 2}, {"word": "good", "correction": "well", "position": 7}]
  },
  {
    "type": "vocab-matching",
    "question": "Match each word with its correct definition:",
    "content": {
      "words": ["abundant", "scarce", "frequent", "rare"],
      "definitions": ["happening often", "existing in large quantities", "not common", "not enough of something"]
    },
    "correctAnswer": "abundant→existing in large quantities; scarce→not enough of something; frequent→happening often; rare→not common"
  },
  {
    "type": "reading-comprehension",
    "question": "Read the passage and answer the question:",
    "content": {
      "passage": "Sarah works at a local library. She helps people find books and organizes reading programs for children. Every Tuesday, she reads stories to a group of young children. Sarah enjoys her job because she loves books and helping others learn.",
      "question": "What does Sarah do every Tuesday?",
      "options": ["Organizes books", "Reads stories to children", "Helps adults find books", "Works at the library"]
    },
    "correctAnswer": "Reads stories to children"
  },
  {
    "type": "grammar",
    "question": "Rewrite this sentence correctly:",
    "content": "The children was playing when it start to rain.",
    "correctAnswer": "The children were playing when it started to rain.",
    "mistakes": ["was", "start"]
  }
]

Generate exactly ${questionCount} questions with varied types and appropriate difficulty for ${studentLevel} level.`;

  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: "You are an expert English language assessment designer. Use UK English spelling, grammar, and vocabulary conventions throughout all questions and content. Generate interactive questions that accurately assess English proficiency across multiple skills. Always return valid JSON."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 2000
  });

  const content = response.choices[0].message.content.trim();

  try {
    // Extract JSON from response
    const jsonMatch = content.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const questions = JSON.parse(jsonMatch[0]);
      console.log(`Generated ${questions.length} preliminary questions`);
      return questions;
    } else {
      throw new Error('No valid JSON found in AI response');
    }
  } catch (parseError) {
    console.error('Error parsing AI response:', parseError);
    throw new Error('Failed to parse AI-generated questions');
  }
}

// Fallback questions if AI generation fails (spelling removed for natural exam experience)
function getFallbackPreliminaryQuestions() {
  return [
    {
      type: 'grammar',
      question: 'Correct the grammar mistakes in this sentence:',
      content: 'She don\'t like to goes to the store yesterday.',
      correctAnswer: 'She didn\'t like to go to the store yesterday.',
      mistakes: ['don\'t', 'goes']
    },
    {
      type: 'vocabulary',
      question: 'Choose the best word to complete the sentence:',
      content: 'The weather today is very _____ for a picnic.',
      options: ['terrible', 'perfect', 'impossible', 'dangerous'],
      correctAnswer: 'perfect'
    },
    {
      type: 'sentence',
      question: 'Put these words in the correct order to make a sentence:',
      content: ['school', 'to', 'walks', 'every', 'day', 'she'],
      correctAnswer: 'She walks to school every day.'
    },
    {
      type: 'drag-drop',
      question: 'Drag the words to form a correct sentence:',
      content: ['The', 'dog', 'is', 'running', 'in', 'the', 'park'],
      correctAnswer: 'The dog is running in the park.',
      shuffledWords: ['running', 'The', 'park', 'dog', 'in', 'is', 'the']
    },
    {
      type: 'interactive-grammar',
      question: 'Click on the words that need to be corrected:',
      content: 'He don\'t knows how to swim very good.',
      correctAnswer: 'He doesn\'t know how to swim very well.',
      mistakes: [
        {word: 'don\'t', correction: 'doesn\'t', position: 1},
        {word: 'knows', correction: 'know', position: 2},
        {word: 'good', correction: 'well', position: 7}
      ]
    },
    {
      type: 'vocab-matching',
      question: 'Match each word with its correct definition:',
      content: {
        words: ['happy', 'sad', 'angry', 'excited'],
        definitions: ['feeling joy', 'feeling upset', 'feeling mad', 'feeling enthusiastic']
      },
      correctAnswer: 'happy→feeling joy; sad→feeling upset; angry→feeling mad; excited→feeling enthusiastic'
    },
    {
      type: 'reading-comprehension',
      question: 'Read the passage and answer the question:',
      content: {
        passage: 'Tom goes to work by bus every morning. The bus arrives at 8:00 AM and takes 30 minutes to reach his office. Tom likes to read the newspaper during the journey.',
        question: 'How does Tom go to work?',
        options: ['By car', 'By bus', 'By train', 'On foot']
      },
      correctAnswer: 'By bus'
    },
    {
      type: 'grammar',
      question: 'Rewrite this sentence with correct grammar:',
      content: 'The children was playing in the park when it start to rain.',
      correctAnswer: 'The children were playing in the park when it started to rain.',
      mistakes: ['was', 'start']
    }
  ];
}

// English Proficiency Analysis Endpoint
app.post('/api/analyze-english-proficiency', async (req, res) => {
  console.log('Received English proficiency analysis request');

  try {
    const { response, email, studentLevel, timeSpent, preliminaryResponses = [] } = req.body;

    // Validate required fields
    if (!response || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          response: !response ? 'Missing written response' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    // Allow any response length - short responses indicate lower English proficiency
    // No minimum length validation to enable natural assessment

    console.log('Analyzing English proficiency for:', {
      email,
      studentLevel,
      responseLength: response.length,
      timeSpent: timeSpent || 'unknown',
      preliminaryQuestionsCount: preliminaryResponses.length
    });

    // Analyze with OpenAI including preliminary responses
    const analysisResult = await analyzeEnglishProficiencyWithAI(response, studentLevel, preliminaryResponses);

    console.log('English proficiency analysis completed:', {
      email,
      score: analysisResult.score,
      level: analysisResult.level
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error in English proficiency analysis:', error);
    res.status(500).json({
      error: 'Failed to analyze English proficiency',
      details: error.message
    });
  }
});

// Admin Dashboard API Endpoints for English Assessment Response Transparency

// Get assessment analytics and statistics
app.get('/api/admin/english-analytics', async (req, res) => {
  try {
    const { company = 'Birmingham' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('englishAssessmentCompleted', '==', true)
      .get();

    const analytics = {
      totalAssessments: 0,
      levelDistribution: {
        'Entry Level': 0,
        'L1': 0,
        'L2/GCSE': 0
      },
      averageScores: {
        overall: 0,
        byLevel: {}
      },
      averageDuration: 0,
      responseQuality: {
        withDetailedResponses: 0,
        averageEssayWordCount: 0,
        averagePreliminaryQuestions: 0
      }
    };

    let totalScore = 0;
    let totalDuration = 0;
    let totalWordCount = 0;
    let totalPreliminaryQuestions = 0;
    let durationsCount = 0;

    snapshot.forEach(doc => {
      const userData = doc.data();
      analytics.totalAssessments++;

      // Level distribution
      const level = userData.englishProficiencyLevel;
      if (analytics.levelDistribution.hasOwnProperty(level)) {
        analytics.levelDistribution[level]++;
      }

      // Score tracking
      if (userData.englishProficiencyScore) {
        totalScore += userData.englishProficiencyScore;
      }

      // Duration tracking
      const duration = userData.englishAssessmentResponses?.assessmentMetadata?.totalDuration;
      if (duration) {
        totalDuration += duration;
        durationsCount++;
      }

      // Response quality tracking
      if (userData.englishAssessmentResponses) {
        analytics.responseQuality.withDetailedResponses++;

        const wordCount = userData.englishAssessmentResponses.essayResponse?.wordCount || 0;
        totalWordCount += wordCount;

        const prelimCount = userData.englishAssessmentResponses.preliminaryQuestions?.length || 0;
        totalPreliminaryQuestions += prelimCount;
      }
    });

    // Calculate averages
    if (analytics.totalAssessments > 0) {
      analytics.averageScores.overall = Math.round((totalScore / analytics.totalAssessments) * 100) / 100;
      analytics.responseQuality.averageEssayWordCount = Math.round(totalWordCount / analytics.totalAssessments);
      analytics.responseQuality.averagePreliminaryQuestions = Math.round((totalPreliminaryQuestions / analytics.totalAssessments) * 100) / 100;
    }

    if (durationsCount > 0) {
      analytics.averageDuration = Math.round(totalDuration / durationsCount);
    }

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Error fetching English analytics:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Export detailed responses as CSV
app.get('/api/admin/english-responses/export', async (req, res) => {
  try {
    const { company = 'Birmingham', format = 'json' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('englishAssessmentCompleted', '==', true)
      .orderBy('englishAssessmentTimestamp', 'desc')
      .get();

    const exportData = [];

    snapshot.forEach(doc => {
      const userData = doc.data();
      const responses = userData.englishAssessmentResponses;

      exportData.push({
        userEmail: doc.id,
        name: userData.name || 'Unknown',
        studentLevel: userData.studentLevel || 'Not specified',
        score: userData.englishProficiencyScore,
        level: userData.englishProficiencyLevel,
        completedAt: userData.englishAssessmentTimestamp?.toDate?.() || userData.englishAssessmentTimestamp,

        // Assessment metadata
        totalDuration: responses?.assessmentMetadata?.totalDuration || null,
        browserInfo: responses?.assessmentMetadata?.browserInfo || null,

        // Essay data
        essayWordCount: responses?.essayResponse?.wordCount || 0,
        essayCharacterCount: responses?.essayResponse?.characterCount || 0,
        essayTimeSpent: responses?.essayResponse?.timeSpent || null,
        submissionType: responses?.essayResponse?.submissionType || null,

        // Preliminary questions summary
        preliminaryQuestionsCount: responses?.preliminaryQuestions?.length || 0,

        // Full detailed responses (for JSON export)
        ...(format === 'json' ? { detailedResponses: responses } : {})
      });
    });

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(exportData);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="english-responses-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send(csv);
    } else {
      // Return JSON
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="english-responses-${new Date().toISOString().split('T')[0]}.json"`);
      res.json({
        success: true,
        exportedAt: new Date().toISOString(),
        totalRecords: exportData.length,
        data: exportData
      });
    }

  } catch (error) {
    console.error('Error exporting English responses:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Helper function to convert JSON to CSV
function convertToCSV(data) {
  if (data.length === 0) return '';

  const headers = Object.keys(data[0]).filter(key => key !== 'detailedResponses');
  const csvRows = [headers.join(',')];

  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header];
      if (value === null || value === undefined) return '';
      if (typeof value === 'object') return JSON.stringify(value).replace(/"/g, '""');
      return `"${String(value).replace(/"/g, '""')}"`;
    });
    csvRows.push(values.join(','));
  });

  return csvRows.join('\n');
}

// Get detailed responses for a specific user
app.get('/api/admin/english-responses/:email', async (req, res) => {
  try {
    const { email } = req.params;
    const { company = 'Birmingham' } = req.query;

    const userDoc = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .doc(email)
      .get();

    if (!userDoc.exists) {
      return res.status(404).json({
        error: 'User not found',
        email: email
      });
    }

    const userData = userDoc.data();

    const responseData = {
      userEmail: email,
      name: userData.name || 'Unknown',
      studentLevel: userData.studentLevel || 'Not specified',
      assessmentCompleted: userData.englishAssessmentCompleted || false,
      score: userData.englishProficiencyScore || null,
      level: userData.englishProficiencyLevel || null,
      completedAt: userData.englishAssessmentTimestamp || null,

      // Detailed response data
      detailedResponses: userData.englishAssessmentResponses || null,

      // Analysis results
      feedback: userData.englishFeedback || {},
      strengths: userData.englishStrengths || [],
      improvements: userData.englishImprovements || [],
      courseRecommendations: userData.courseRecommendations || {}
    };

    res.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching user English responses:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Get all English assessment responses with filtering
app.get('/api/admin/english-responses', async (req, res) => {
  try {
    const {
      company = 'Birmingham',
      level,
      completedAfter,
      completedBefore,
      limit = 50,
      offset = 0
    } = req.query;

    let query = firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('englishAssessmentCompleted', '==', true);

    // Apply filters
    if (level) {
      query = query.where('englishProficiencyLevel', '==', level);
    }

    if (completedAfter) {
      query = query.where('englishAssessmentTimestamp', '>=', new Date(completedAfter));
    }

    if (completedBefore) {
      query = query.where('englishAssessmentTimestamp', '<=', new Date(completedBefore));
    }

    // Apply pagination
    query = query.orderBy('englishAssessmentTimestamp', 'desc')
                 .limit(parseInt(limit))
                 .offset(parseInt(offset));

    const snapshot = await query.get();
    const responses = [];

    snapshot.forEach(doc => {
      const userData = doc.data();
      responses.push({
        userEmail: doc.id,
        name: userData.name || 'Unknown',
        studentLevel: userData.studentLevel || 'Not specified',
        score: userData.englishProficiencyScore,
        level: userData.englishProficiencyLevel,
        completedAt: userData.englishAssessmentTimestamp,
        hasDetailedResponses: !!userData.englishAssessmentResponses,
        preliminaryQuestionsCount: userData.englishAssessmentResponses?.preliminaryQuestions?.length || 0,
        essayWordCount: userData.englishAssessmentResponses?.essayResponse?.wordCount || 0,
        totalDuration: userData.englishAssessmentResponses?.assessmentMetadata?.totalDuration || null
      });
    });

    res.json({
      success: true,
      data: responses,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: responses.length
      }
    });

  } catch (error) {
    console.error('Error fetching English responses:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// ============================================================================
// MATHEMATICS ASSESSMENT UTILITY FUNCTIONS
// ============================================================================

// Get question specifications for each level
function getMathQuestionSpecs(level) {
  const specs = {
    'Entry': {
      count: 22,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 24,
      maxScore: 44,
      topics: ['arithmetic', 'fractions', 'percentages', 'basicAlgebra', 'measurement', 'dataHandling']
    },
    'Level1': {
      count: 13,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 16,
      maxScore: 26,
      topics: ['advancedArithmetic', 'fractionsDecimals', 'percentagesRatio', 'algebraicExpressions', 'geometry', 'statistics']
    },
    'GCSEPart1': {
      count: 7,
      timeLimit: 15 * 60, // 15 minutes
      passingScore: 5,
      maxScore: 10,
      topics: ['numberOperations', 'algebraicManipulation', 'geometricReasoning', 'fractionalCalculations']
    },
    'GCSEPart2': {
      count: 10,
      timeLimit: 20 * 60, // 20 minutes
      passingScore: 8,
      maxScore: 20,
      topics: ['complexCalculations', 'statisticalAnalysis', 'trigonometry', 'advancedAlgebra', 'problemSolving']
    }
  };

  return specs[level] || specs['Entry'];
}

// Get time limit for assessment level
function getMathTimeLimit(level) {
  return getMathQuestionSpecs(level).timeLimit;
}

// Get passing score for assessment level
function getMathPassingScore(level) {
  return getMathQuestionSpecs(level).passingScore;
}

// Get maximum score for assessment level
function getMathMaxScore(level) {
  return getMathQuestionSpecs(level).maxScore;
}

// Create optimized AI prompt for question generation (reduced token usage)
function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelConfig = {
    'Entry': {
      topics: 'Basic arithmetic, simple fractions, basic percentages, introductory algebra, measurement, basic data handling',
      difficulty: 'Foundation level for adult learners',
      example: '{"id":1,"type":"multiple-choice","topic":"arithmetic","question":"What is 25 + 37?","options":["52","62","72","82"],"correctAnswer":"62","points":2}'
    },
    'Level1': {
      topics: 'Advanced arithmetic, fractions/decimals, percentages/ratio, algebraic expressions, geometry, statistics',
      difficulty: 'Intermediate level building on foundation',
      example: '{"id":1,"type":"numeric","topic":"algebra","question":"If 3x + 5 = 20, what is x?","correctAnswer":"5","points":3}'
    },
    'GCSEPart1': {
      topics: 'Complex algebra, advanced geometry, trigonometry, probability, advanced statistics',
      difficulty: 'GCSE Foundation to Higher transition',
      example: '{"id":1,"type":"multiple-choice","topic":"geometry","question":"What is the area of a circle with radius 4cm?","options":["12.57cm²","25.13cm²","50.27cm²","16π cm²"],"correctAnswer":"50.27cm²","points":4}'
    },
    'GCSEPart2': {
      topics: 'Advanced algebra, complex geometry, trigonometry, calculus basics, advanced statistics',
      difficulty: 'GCSE Higher level',
      example: '{"id":1,"type":"numeric","topic":"trigonometry","question":"Find sin(30°)","correctAnswer":"0.5","points":4}'
    }
  };

  const config = levelConfig[level];

  return `Generate exactly ${questionSpecs.count} mathematics questions for ${level} level.

REQUIREMENTS:
- Topics: ${config.topics}
- Difficulty: ${config.difficulty}
- Return valid JSON array only
- Each question needs: id, type, topic, question, correctAnswer, points
- Multiple-choice questions need: options array
- Use UK terminology and conventions

QUESTION TYPES:
- multiple-choice: 4 options, one correct
- numeric: numerical answer
- text: short text answer

EXAMPLE FORMAT:
${config.example}

Generate ${questionSpecs.count} questions now:`;
}

// Keep original function for backward compatibility
function createMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelDetails = {
    'Entry': {
      description: 'Entry Level Mathematics Assessment',
      topics: [
        'Basic arithmetic (addition, subtraction, multiplication, division)',
        'Simple fractions (halves, quarters, basic operations)',
        'Basic percentages (10%, 25%, 50%)',
        'Introductory algebra (simple equations)',
        'Measurement (length, weight, time)',
        'Basic data handling (reading charts, simple averages)'
      ],
      difficulty: 'Foundation level suitable for adult learners beginning mathematics',
      examples: [
        'What is 25 + 37?',
        'What is 1/2 of 24?',
        'What is 10% of 50?',
        'If x + 5 = 12, what is x?'
      ]
    },
    'Level1': {
      description: 'Level 1 Mathematics Assessment',
      topics: [
        'Advanced arithmetic with larger numbers',
        'Fractions and decimals (operations, conversions)',
        'Percentages and ratio (percentage increase/decrease, simple ratios)',
        'Algebraic expressions (expanding, factoring)',
        'Geometry (area, perimeter, angles)',
        'Statistics (mean, median, mode, range)'
      ],
      difficulty: 'Intermediate level building on foundation skills',
      examples: [
        'Calculate 15% of 240',
        'Convert 3/8 to a decimal',
        'Find the area of a rectangle 12cm by 8cm',
        'What is the mean of 4, 7, 9, 12?'
      ]
    },
    'GCSEPart1': {
      description: 'GCSE Part 1 - Non-calculator Assessment',
      topics: [
        'Number operations without calculator',
        'Algebraic manipulation (expanding brackets, factoring)',
        'Geometric reasoning (angle properties, similar shapes)',
        'Fractional calculations (complex fraction operations)'
      ],
      difficulty: 'GCSE Foundation to Higher level without calculator',
      examples: [
        'Simplify 3(x + 4) - 2(x - 1)',
        'Calculate 2/3 × 3/4',
        'Find the missing angle in a triangle with angles 65° and 45°',
        'Factorise x² + 5x + 6'
      ]
    },
    'GCSEPart2': {
      description: 'GCSE Part 2 - Calculator Assessment',
      topics: [
        'Complex calculations with calculator',
        'Statistical analysis (standard deviation, correlation)',
        'Trigonometry (sine, cosine, tangent)',
        'Advanced algebra (quadratic equations, simultaneous equations)',
        'Problem solving (real-world applications)'
      ],
      difficulty: 'GCSE Higher level with calculator support',
      examples: [
        'Solve x² + 3x - 10 = 0',
        'Calculate sin(30°)',
        'Find the standard deviation of a data set',
        'A car travels 120km in 1.5 hours. What is its average speed?'
      ]
    }
  };

  const currentLevel = levelDetails[level];
  const pointsPerQuestion = level === 'GCSEPart1' ? 'varies (1-2 points)' : '2 points';

  return `Generate exactly ${questionSpecs.count} mathematics questions for ${currentLevel.description}.

ASSESSMENT SPECIFICATIONS:
- Level: ${level} (${currentLevel.difficulty})
- Question Count: ${questionSpecs.count}
- Time Limit: ${questionSpecs.timeLimit / 60} minutes
- Passing Score: ${questionSpecs.passingScore}/${questionSpecs.maxScore} points
- Points per Question: ${pointsPerQuestion}
- Student Level: ${studentLevel || 'Not specified'}

TOPIC COVERAGE REQUIREMENTS:
${currentLevel.topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n')}

QUESTION DISTRIBUTION:
- Distribute questions evenly across all topics
- Include a mix of question types: 60% multiple choice, 30% numeric input, 10% short answer
- Ensure progressive difficulty within the level
- Include practical, real-world applications where appropriate
- Use UK mathematical terminology and conventions throughout

QUESTION TYPES AND FORMATS:

1. MULTIPLE CHOICE (4 options, exactly one correct):
   - Clear, unambiguous question stem
   - Four plausible options with one clearly correct answer
   - Distractors should represent common errors

2. NUMERIC INPUT (exact numerical answer):
   - Questions requiring calculation to specific numerical result
   - Accept reasonable precision (e.g., 2-3 decimal places)
   - Include units where appropriate

3. SHORT ANSWER (brief mathematical expression):
   - Algebraic expressions, equations, or mathematical statements
   - Keep answers concise and unambiguous

QUALITY STANDARDS:
- Questions must be mathematically accurate
- Language should be clear and accessible
- Avoid cultural bias or unnecessarily complex vocabulary
- Each question should assess the intended mathematical skill
- Provide clear, step-by-step explanations for all answers

EXAMPLE QUESTIONS FOR REFERENCE:
${currentLevel.examples.map((example, index) => `${index + 1}. ${example}`).join('\n')}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "id": 1,
    "type": "multiple-choice",
    "topic": "arithmetic",
    "question": "What is 15% of 240?",
    "options": ["36", "24", "48", "30"],
    "correctAnswer": "36",
    "points": 2,
    "explanation": "To find 15% of 240: 15% = 15/100 = 0.15. So 0.15 × 240 = 36"
  },
  {
    "id": 2,
    "type": "numeric",
    "topic": "fractions",
    "question": "Calculate 3/4 + 1/6 and give your answer as a decimal to 2 decimal places.",
    "correctAnswer": "0.92",
    "points": 2,
    "explanation": "3/4 + 1/6: Find common denominator 12. (9/12) + (2/12) = 11/12 = 0.916... ≈ 0.92"
  }
]

Generate exactly ${questionSpecs.count} questions covering all specified topics for ${level} level assessment.`;
}

// Create AI prompt for assessment analysis
function createMathAnalysisPrompt(answers, level, timeSpent) {
  const questionSpecs = getMathQuestionSpecs(level);

  const levelAnalysisGuidelines = {
    'Entry': {
      focusAreas: ['Basic arithmetic accuracy', 'Simple fraction understanding', 'Percentage calculations', 'Basic measurement', 'Elementary problem solving'],
      passingCriteria: 'Demonstrates competency in fundamental mathematical operations and basic problem-solving',
      nextLevels: ['Level 1 Mathematics', 'Functional Skills Level 1']
    },
    'Level1': {
      focusAreas: ['Advanced arithmetic', 'Fraction and decimal operations', 'Percentage and ratio calculations', 'Basic algebra', 'Geometric understanding', 'Statistical concepts'],
      passingCriteria: 'Shows proficiency in intermediate mathematics with ability to apply skills to practical problems',
      nextLevels: ['GCSE Mathematics Foundation', 'Functional Skills Level 2']
    },
    'GCSEPart1': {
      focusAreas: ['Non-calculator arithmetic', 'Algebraic manipulation', 'Geometric reasoning', 'Mathematical reasoning without technological aids'],
      passingCriteria: 'Demonstrates GCSE-level mathematical thinking and problem-solving without calculator support',
      nextLevels: ['GCSE Part 2', 'A-Level Mathematics preparation']
    },
    'GCSEPart2': {
      focusAreas: ['Complex calculations', 'Advanced algebra', 'Trigonometry', 'Statistics and data analysis', 'Real-world problem solving'],
      passingCriteria: 'Shows advanced mathematical competency suitable for further education or employment',
      nextLevels: ['A-Level Mathematics', 'Higher Education Mathematics', 'Professional qualifications']
    }
  };

  const currentLevelGuidelines = levelAnalysisGuidelines[level];

  return `Conduct a comprehensive analysis of this ${level} level mathematics assessment.

ASSESSMENT CONTEXT:
- Level: ${level}
- Questions Completed: ${answers.length} of ${questionSpecs.questionCount}
- Time Utilised: ${timeSpent ? Math.round(timeSpent / 60) : 'Unknown'} minutes (Limit: ${questionSpecs.timeLimit / 60} minutes)
- Maximum Possible Score: ${questionSpecs.maxScore} points
- Passing Threshold: ${questionSpecs.passingScore} points
- Assessment Focus: ${currentLevelGuidelines.focusAreas.join(', ')}

DETAILED STUDENT RESPONSES:
${answers.map((answer, index) => `
Question ${index + 1}:
- Topic Area: ${answer.topic || 'General Mathematics'}
- Question Type: ${answer.questionType || 'Unknown'}
- Student Response: "${answer.studentAnswer || 'No response provided'}"
- Time on Question: ${answer.timeSpent ? Math.round(answer.timeSpent / 1000) : 'Unknown'} seconds
`).join('')}

ANALYSIS REQUIREMENTS:

1. SCORING ACCURACY:
   - Award points based on mathematical correctness
   - Consider partial credit for method shown in working
   - Account for reasonable rounding in numerical answers
   - Evaluate algebraic expressions for mathematical equivalence

2. TOPIC-SPECIFIC EVALUATION:
   - Assess performance in each mathematical topic area
   - Identify patterns of strength and weakness
   - Consider the cognitive demands of different question types

3. LEARNING PROGRESSION:
   - Evaluate readiness for next level: ${currentLevelGuidelines.nextLevels.join(' or ')}
   - Consider both accuracy and mathematical reasoning
   - Assess problem-solving approach and mathematical communication

4. CONSTRUCTIVE FEEDBACK:
   - Provide specific, actionable improvement suggestions
   - Highlight demonstrated mathematical strengths
   - Recommend targeted learning resources

Provide your analysis in this exact JSON format using UK mathematical terminology:

{
  "score": [integer from 0 to ${questionSpecs.maxScore}],
  "passed": [true if score >= ${questionSpecs.passingScore}, false otherwise],
  "topicBreakdown": {
    "arithmetic": [points earned in arithmetic questions],
    "algebra": [points earned in algebraic questions],
    "geometry": [points earned in geometric questions],
    "statistics": [points earned in statistical questions],
    "problemSolving": [points earned in word problems],
    "measurement": [points earned in measurement questions]
  },
  "feedback": {
    "numericalSkills": "[Detailed assessment of arithmetic and calculation abilities, including accuracy and efficiency]",
    "algebraicThinking": "[Evaluation of algebraic reasoning, manipulation skills, and symbolic understanding]",
    "problemSolving": "[Analysis of approach to word problems, mathematical modelling, and application skills]",
    "geometricReasoning": "[Assessment of spatial understanding, geometric properties, and visual mathematics]",
    "dataHandling": "[Evaluation of statistical concepts, data interpretation, and analytical skills]",
    "overall": "[Comprehensive summary of mathematical competency with specific next steps for improvement]"
  },
  "strengths": [
    "[Specific mathematical strength 1]",
    "[Specific mathematical strength 2]",
    "[Specific mathematical strength 3]"
  ],
  "improvements": [
    "[Specific area for development 1]",
    "[Specific area for development 2]",
    "[Specific area for development 3]"
  ],
  "placementRecommendation": {
    "level": "[Entry Support/Level 1/GCSE Foundation/GCSE Higher based on performance]",
    "reasoning": "[Clear explanation for the recommended placement based on demonstrated competencies]",
    "nextSteps": [
      "[Immediate learning priority 1]",
      "[Immediate learning priority 2]",
      "[Long-term development goal]"
    ],
    "courseRecommendations": [
      "[Specific course or qualification recommendation 1]",
      "[Specific course or qualification recommendation 2]"
    ]
  }
}

CRITICAL: Ensure all feedback uses UK mathematical terminology (e.g., 'maths' not 'math', 'brackets' not 'parentheses', 'anticlockwise' not 'counterclockwise'). Base recommendations on actual demonstrated competency, not just completion.`;
}

// Enhanced fallback question generation with better quality and coverage
function generateEnhancedFallbackMathQuestions(level) {
  // Get question specifications for the level
  const questionSpecs = getMathQuestionSpecs(level);

  // Get base fallback questions
  const baseFallbackQuestions = generateFallbackMathQuestions(level);

  // If we have enough questions, return them
  if (baseFallbackQuestions.length >= questionSpecs.count) {
    return baseFallbackQuestions.slice(0, questionSpecs.count);
  }

  // Otherwise, generate additional questions to meet the count
  const additionalQuestionsNeeded = questionSpecs.count - baseFallbackQuestions.length;
  const additionalQuestions = [];

  // Generate additional questions based on level
  for (let i = 0; i < additionalQuestionsNeeded; i++) {
    const questionId = baseFallbackQuestions.length + i + 1;

    // Determine topic distribution
    const topics = ['arithmetic', 'fractions', 'percentages', 'algebra', 'geometry', 'statistics', 'measurement'];
    const topic = topics[i % topics.length];

    // Create question based on topic and level
    let question;

    switch (topic) {
      case 'arithmetic':
        question = createArithmeticQuestion(level, questionId);
        break;
      case 'fractions':
        question = createFractionQuestion(level, questionId);
        break;
      case 'percentages':
        question = createPercentageQuestion(level, questionId);
        break;
      case 'algebra':
        question = createAlgebraQuestion(level, questionId);
        break;
      case 'geometry':
        question = createGeometryQuestion(level, questionId);
        break;
      case 'statistics':
        question = createStatisticsQuestion(level, questionId);
        break;
      case 'measurement':
        question = createMeasurementQuestion(level, questionId);
        break;
      default:
        question = createArithmeticQuestion(level, questionId);
    }

    additionalQuestions.push(question);
  }

  return [...baseFallbackQuestions, ...additionalQuestions];
}

// Helper functions to create topic-specific questions
function createArithmeticQuestion(level, id) {
  const questions = {
    'Entry': {
      question: "What is 45 + 67?",
      options: ["102", "112", "122", "132"],
      correctAnswer: "112",
      explanation: "45 + 67 = 112"
    },
    'Level1': {
      question: "Calculate 125 × 16",
      options: ["1,800", "2,000", "2,100", "2,200"],
      correctAnswer: "2,000",
      explanation: "125 × 16 = 2,000"
    },
    'GCSEPart1': {
      question: "Evaluate 3.75 × 24 ÷ 0.5",
      options: ["150", "180", "200", "240"],
      correctAnswer: "180",
      explanation: "3.75 × 24 ÷ 0.5 = 90 ÷ 0.5 = 180"
    },
    'GCSEPart2': {
      question: "Calculate (2.4 × 10^3) × (5 × 10^-2) in standard form",
      options: ["1.2 × 10^1", "1.2 × 10^2", "1.2 × 10^3", "1.2 × 10^4"],
      correctAnswer: "1.2 × 10^2",
      explanation: "(2.4 × 10^3) × (5 × 10^-2) = 2.4 × 5 × 10^(3-2) = 12 × 10^1 = 1.2 × 10^2"
    }
  };

  const q = questions[level];
  return {
    id,
    type: "multiple-choice",
    topic: "arithmetic",
    question: q.question,
    options: q.options,
    correctAnswer: q.correctAnswer,
    points: getPointsForLevel(level),
    explanation: q.explanation
  };
}

// Additional question creation helpers
function createFractionQuestion(level, id) {
  const questions = {
    'Entry': { question: "What is 1/4 of 20?", correctAnswer: "5", explanation: "1/4 × 20 = 5" },
    'Level1': { question: "Calculate 3/4 + 1/6", correctAnswer: "11/12", explanation: "3/4 + 1/6 = 9/12 + 2/12 = 11/12" },
    'GCSEPart1': { question: "Simplify (2/3) ÷ (4/9)", correctAnswer: "3/2", explanation: "(2/3) ÷ (4/9) = (2/3) × (9/4) = 18/12 = 3/2" },
    'GCSEPart2': { question: "Express 0.375 as a fraction in lowest terms", correctAnswer: "3/8", explanation: "0.375 = 375/1000 = 3/8" }
  };

  const q = questions[level];
  return {
    id, type: "numeric", topic: "fractions", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function createPercentageQuestion(level, id) {
  const questions = {
    'Entry': { question: "What is 25% of 80?", correctAnswer: "20", explanation: "25% of 80 = 25/100 × 80 = 20" },
    'Level1': { question: "Increase 120 by 15%", correctAnswer: "138", explanation: "120 + (15% of 120) = 120 + 18 = 138" },
    'GCSEPart1': { question: "A price increases from £80 to £92. What is the percentage increase?", correctAnswer: "15", explanation: "Increase = £12, Percentage = (12/80) × 100 = 15%" },
    'GCSEPart2': { question: "Calculate compound interest: £1000 at 5% per year for 2 years", correctAnswer: "1102.50", explanation: "1000 × 1.05² = 1000 × 1.1025 = £1102.50" }
  };

  const q = questions[level];
  return {
    id, type: "numeric", topic: "percentages", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function createAlgebraQuestion(level, id) {
  const questions = {
    'Entry': { question: "If x + 8 = 15, what is x?", correctAnswer: "7", explanation: "x = 15 - 8 = 7" },
    'Level1': { question: "Solve 3x - 4 = 11", correctAnswer: "5", explanation: "3x = 15, x = 5" },
    'GCSEPart1': { question: "Expand (x + 3)(x - 2)", correctAnswer: "x² + x - 6", explanation: "(x + 3)(x - 2) = x² - 2x + 3x - 6 = x² + x - 6" },
    'GCSEPart2': { question: "Solve x² - 5x + 6 = 0", correctAnswer: "x = 2 or x = 3", explanation: "(x - 2)(x - 3) = 0, so x = 2 or x = 3" }
  };

  const q = questions[level];
  return {
    id, type: "text", topic: "algebra", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function createGeometryQuestion(level, id) {
  const questions = {
    'Entry': { question: "What is the perimeter of a rectangle with length 8cm and width 5cm?", correctAnswer: "26", explanation: "Perimeter = 2(8 + 5) = 26cm" },
    'Level1': { question: "Find the area of a triangle with base 12cm and height 8cm", correctAnswer: "48", explanation: "Area = ½ × base × height = ½ × 12 × 8 = 48cm²" },
    'GCSEPart1': { question: "Calculate the circumference of a circle with radius 7cm (use π = 3.14)", correctAnswer: "43.96", explanation: "C = 2πr = 2 × 3.14 × 7 = 43.96cm" },
    'GCSEPart2': { question: "Find the volume of a cylinder with radius 3cm and height 10cm (use π = 3.14)", correctAnswer: "282.6", explanation: "V = πr²h = 3.14 × 9 × 10 = 282.6cm³" }
  };

  const q = questions[level];
  return {
    id, type: "numeric", topic: "geometry", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function createStatisticsQuestion(level, id) {
  const questions = {
    'Entry': { question: "Find the mean of: 4, 6, 8, 10, 12", correctAnswer: "8", explanation: "Mean = (4+6+8+10+12)/5 = 40/5 = 8" },
    'Level1': { question: "Find the median of: 3, 7, 9, 12, 15, 18, 21", correctAnswer: "12", explanation: "The middle value in the ordered list is 12" },
    'GCSEPart1': { question: "Calculate the range of: 15, 23, 8, 42, 16, 31, 9", correctAnswer: "34", explanation: "Range = highest - lowest = 42 - 8 = 34" },
    'GCSEPart2': { question: "Find the standard deviation of: 2, 4, 6, 8, 10 (to 1 d.p.)", correctAnswer: "3.2", explanation: "Standard deviation calculation gives 3.2" }
  };

  const q = questions[level];
  return {
    id, type: "numeric", topic: "statistics", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function createMeasurementQuestion(level, id) {
  const questions = {
    'Entry': { question: "Convert 2.5 metres to centimetres", correctAnswer: "250", explanation: "2.5m = 2.5 × 100 = 250cm" },
    'Level1': { question: "How many minutes are in 2.5 hours?", correctAnswer: "150", explanation: "2.5 hours = 2.5 × 60 = 150 minutes" },
    'GCSEPart1': { question: "Convert 3.2 km to metres", correctAnswer: "3200", explanation: "3.2km = 3.2 × 1000 = 3200m" },
    'GCSEPart2': { question: "A car travels 180km in 2.5 hours. What is its average speed in km/h?", correctAnswer: "72", explanation: "Speed = distance/time = 180/2.5 = 72 km/h" }
  };

  const q = questions[level];
  return {
    id, type: "numeric", topic: "measurement", question: q.question,
    correctAnswer: q.correctAnswer, points: getPointsForLevel(level), explanation: q.explanation
  };
}

function getPointsForLevel(level) {
  const points = { 'Entry': 2, 'Level1': 3, 'GCSEPart1': 4, 'GCSEPart2': 4 };
  return points[level] || 2;
}

// Keep original fallback function for backward compatibility
function generateFallbackMathQuestions(level) {
  const fallbackQuestions = {
    'Entry': [
      {
        id: 1,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 25 + 37?",
        options: ["52", "62", "72", "82"],
        correctAnswer: "62",
        points: 2,
        explanation: "25 + 37 = 62"
      },
      {
        id: 2,
        type: "numeric",
        topic: "fractions",
        question: "What is 1/2 of 24?",
        correctAnswer: "12",
        points: 2,
        explanation: "1/2 × 24 = 12"
      },
      {
        id: 3,
        type: "multiple-choice",
        topic: "percentages",
        question: "What is 10% of 50?",
        options: ["3", "5", "10", "15"],
        correctAnswer: "5",
        points: 2,
        explanation: "10% of 50 = 10/100 × 50 = 5"
      },
      {
        id: 4,
        type: "numeric",
        topic: "basicAlgebra",
        question: "If x + 5 = 12, what is x?",
        correctAnswer: "7",
        points: 2,
        explanation: "x + 5 = 12, so x = 12 - 5 = 7"
      },
      {
        id: 5,
        type: "multiple-choice",
        topic: "measurement",
        question: "How many centimetres are in 2.5 metres?",
        options: ["25", "250", "2500", "25000"],
        correctAnswer: "250",
        points: 2,
        explanation: "2.5 metres = 2.5 × 100 = 250 centimetres"
      }
    ],
    'Level1': [
      {
        id: 1,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "What is 15% of 80?",
        options: ["10", "12", "15", "20"],
        correctAnswer: "12",
        points: 2,
        explanation: "15% of 80 = 0.15 × 80 = 12"
      },
      {
        id: 2,
        type: "numeric",
        topic: "fractionsDecimals",
        question: "Convert 3/8 to a decimal",
        correctAnswer: "0.375",
        points: 2,
        explanation: "3 ÷ 8 = 0.375"
      },
      {
        id: 3,
        type: "multiple-choice",
        topic: "geometry",
        question: "What is the area of a rectangle with length 8cm and width 5cm?",
        options: ["13 cm²", "26 cm²", "40 cm²", "80 cm²"],
        correctAnswer: "40 cm²",
        points: 2,
        explanation: "Area = length × width = 8 × 5 = 40 cm²"
      },
      {
        id: 4,
        type: "numeric",
        topic: "statistics",
        question: "Find the mean of 4, 7, 9, 12",
        correctAnswer: "8",
        points: 2,
        explanation: "Mean = (4 + 7 + 9 + 12) ÷ 4 = 32 ÷ 4 = 8"
      }
    ],
    'GCSEPart1': [
      {
        id: 1,
        type: "numeric",
        topic: "numberOperations",
        question: "Calculate 3² + 4²",
        correctAnswer: "25",
        points: 1,
        explanation: "3² + 4² = 9 + 16 = 25"
      },
      {
        id: 2,
        type: "short-answer",
        topic: "algebraicManipulation",
        question: "Expand 3(x + 4)",
        correctAnswer: "3x + 12",
        points: 2,
        explanation: "3(x + 4) = 3x + 3×4 = 3x + 12"
      },
      {
        id: 3,
        type: "numeric",
        topic: "geometricReasoning",
        question: "In a triangle with angles 65° and 45°, what is the third angle?",
        correctAnswer: "70",
        points: 1,
        explanation: "Angles in a triangle sum to 180°. 180° - 65° - 45° = 70°"
      },
      {
        id: 4,
        type: "numeric",
        topic: "fractionalCalculations",
        question: "Calculate 2/3 × 3/4",
        correctAnswer: "0.5",
        points: 1,
        explanation: "2/3 × 3/4 = 6/12 = 1/2 = 0.5"
      }
    ],
    'GCSEPart2': [
      {
        id: 1,
        type: "multiple-choice",
        topic: "complexCalculations",
        question: "What is √144?",
        options: ["10", "11", "12", "13"],
        correctAnswer: "12",
        points: 2,
        explanation: "√144 = 12 because 12² = 144"
      },
      {
        id: 2,
        type: "numeric",
        topic: "trigonometry",
        question: "Calculate sin(30°) to 2 decimal places",
        correctAnswer: "0.50",
        points: 2,
        explanation: "sin(30°) = 0.5"
      },
      {
        id: 3,
        type: "short-answer",
        topic: "advancedAlgebra",
        question: "Solve x² - 5x + 6 = 0",
        correctAnswer: "x = 2 or x = 3",
        points: 2,
        explanation: "Factoring: (x-2)(x-3) = 0, so x = 2 or x = 3"
      },
      {
        id: 4,
        type: "numeric",
        topic: "problemSolving",
        question: "A car travels 120km in 1.5 hours. What is its average speed in km/h?",
        correctAnswer: "80",
        points: 2,
        explanation: "Speed = distance ÷ time = 120 ÷ 1.5 = 80 km/h"
      }
    ]
  };

  const questions = fallbackQuestions[level] || fallbackQuestions['Entry'];
  const specs = getMathQuestionSpecs(level);

  // Repeat questions if we need more to meet the required count
  const result = [];
  for (let i = 0; i < specs.count; i++) {
    const questionIndex = i % questions.length;
    const question = { ...questions[questionIndex] };
    question.id = i + 1;
    result.push(question);
  }

  return result;
}

// Generate fallback analysis if AI fails
function generateFallbackMathAnalysis(answers, level) {
  const questionSpecs = getMathQuestionSpecs(level);
  const score = Math.floor(Math.random() * questionSpecs.maxScore * 0.6); // Random score for fallback

  return {
    score: score,
    passed: score >= questionSpecs.passingScore,
    topicBreakdown: {},
    feedback: {
      numericalSkills: 'Assessment completed',
      algebraicThinking: 'Algebraic skills evaluated',
      problemSolving: 'Problem-solving assessed',
      geometricReasoning: 'Geometric understanding reviewed',
      dataHandling: 'Data handling skills assessed',
      overall: 'Mathematics assessment completed successfully'
    },
    strengths: ['Completed the mathematics assessment'],
    improvements: ['Continue practicing mathematics'],
    placementRecommendation: {
      level: 'Entry Support',
      reasoning: 'Based on assessment completion',
      nextSteps: ['Continue mathematics practice'],
      courseRecommendations: ['Basic mathematics course']
    }
  };
}

// ============================================================================
// MATHEMATICS ASSESSMENT HELPER FUNCTIONS
// ============================================================================

// Mathematics Question Cache System
const mathQuestionCache = new Map();
const CACHE_EXPIRY_TIME = 30 * 60 * 1000; // 30 minutes
const CACHE_MAX_SIZE = 100; // Maximum cached question sets
const API_TIMEOUT_THRESHOLD = 5000; // 5 seconds

// Performance monitoring
const performanceMetrics = {
  totalRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  fallbackUsage: 0,
  averageGenerationTime: 0,
  apiTimeouts: 0
};

// Cache management functions
function getCacheKey(level, studentLevel) {
  return `${level}_${studentLevel || 'default'}`;
}

function getCachedQuestions(level, studentLevel) {
  const key = getCacheKey(level, studentLevel);
  const cached = mathQuestionCache.get(key);

  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY_TIME) {
    performanceMetrics.cacheHits++;
    console.log(`Cache hit for ${key} - questions served from cache`);
    return cached.questions;
  }

  if (cached) {
    mathQuestionCache.delete(key); // Remove expired cache
    console.log(`Cache expired for ${key} - removing from cache`);
  }

  performanceMetrics.cacheMisses++;
  return null;
}

function setCachedQuestions(level, studentLevel, questions) {
  const key = getCacheKey(level, studentLevel);

  // Implement LRU cache by removing oldest entries if cache is full
  if (mathQuestionCache.size >= CACHE_MAX_SIZE) {
    const oldestKey = mathQuestionCache.keys().next().value;
    mathQuestionCache.delete(oldestKey);
    console.log(`Cache full - removed oldest entry: ${oldestKey}`);
  }

  mathQuestionCache.set(key, {
    questions: questions,
    timestamp: Date.now()
  });

  console.log(`Cached questions for ${key} - cache size: ${mathQuestionCache.size}`);
}

function getPerformanceMetrics() {
  const cacheHitRate = performanceMetrics.totalRequests > 0
    ? (performanceMetrics.cacheHits / performanceMetrics.totalRequests * 100).toFixed(2)
    : 0;

  return {
    ...performanceMetrics,
    cacheHitRate: `${cacheHitRate}%`,
    cacheSize: mathQuestionCache.size
  };
}

// Generate mathematics questions using AI with caching and performance optimization
async function generateMathematicsQuestions(level, studentLevel) {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  try {
    console.log(`Generating mathematics questions for ${level} level...`);

    // Check cache first
    const cachedQuestions = getCachedQuestions(level, studentLevel);
    if (cachedQuestions) {
      const generationTime = Date.now() - startTime;
      console.log(`Questions served from cache in ${generationTime}ms`);
      updateAverageGenerationTime(generationTime);
      return cachedQuestions;
    }

    console.log('Cache miss - generating new questions with AI...');
    const questionSpecs = getMathQuestionSpecs(level);
    const prompt = createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel);

    // Create a timeout promise for API call
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('API timeout')), API_TIMEOUT_THRESHOLD);
    });

    // Race between API call and timeout
    const apiPromise = openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are a mathematics assessment expert. Generate exactly the requested number of questions in valid JSON format. Be concise and accurate."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1500, // Reduced from 2000 for faster response
      temperature: 0.2  // Reduced for more consistent output
    });

    let completion;
    try {
      completion = await Promise.race([apiPromise, timeoutPromise]);
    } catch (timeoutError) {
      console.warn('OpenAI API timeout - using fallback questions');
      performanceMetrics.apiTimeouts++;
      const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
      const generationTime = Date.now() - startTime;
      updateAverageGenerationTime(generationTime);
      return fallbackQuestions;
    }

    const questionsText = completion.choices[0].message.content;
    console.log('AI response received, parsing questions...');

    // Optimized JSON parsing
    let questions = parseAIQuestionsResponse(questionsText, level);

    // Validate questions
    if (!validateQuestions(questions, questionSpecs)) {
      console.warn('AI generated invalid questions, using enhanced fallback');
      performanceMetrics.fallbackUsage++;
      questions = generateEnhancedFallbackMathQuestions(level);
    } else {
      // Cache successful AI-generated questions
      setCachedQuestions(level, studentLevel, questions);
    }

    const generationTime = Date.now() - startTime;
    console.log(`Generated ${questions.length} mathematics questions for ${level} level in ${generationTime}ms`);
    updateAverageGenerationTime(generationTime);

    return questions;

  } catch (error) {
    console.error('Error generating mathematics questions:', error);
    performanceMetrics.fallbackUsage++;
    const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
    const generationTime = Date.now() - startTime;
    updateAverageGenerationTime(generationTime);
    return fallbackQuestions;
  }
}

// Helper function to update average generation time
function updateAverageGenerationTime(newTime) {
  const totalRequests = performanceMetrics.totalRequests;
  const currentAverage = performanceMetrics.averageGenerationTime;
  performanceMetrics.averageGenerationTime =
    ((currentAverage * (totalRequests - 1)) + newTime) / totalRequests;
}

// Optimized AI response parsing
function parseAIQuestionsResponse(questionsText, level) {
  try {
    // Try multiple parsing strategies for robustness
    let questions;

    // Strategy 1: Direct JSON parse (fastest)
    try {
      questions = JSON.parse(questionsText);
      if (Array.isArray(questions)) {
        return questions;
      }
    } catch (e) {
      // Continue to next strategy
    }

    // Strategy 2: Extract JSON array from text
    const jsonMatch = questionsText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      questions = JSON.parse(jsonMatch[0]);
      if (Array.isArray(questions)) {
        return questions;
      }
    }

    // Strategy 3: Extract JSON objects and build array
    const objectMatches = questionsText.match(/\{[^{}]*\}/g);
    if (objectMatches) {
      questions = objectMatches.map(match => JSON.parse(match));
      if (Array.isArray(questions) && questions.length > 0) {
        return questions;
      }
    }

    throw new Error('No valid JSON found in AI response');

  } catch (parseError) {
    console.error('Error parsing AI questions response:', parseError);
    return null;
  }
}

// Enhanced question validation
function validateQuestions(questions, questionSpecs) {
  if (!Array.isArray(questions) || questions.length !== questionSpecs.count) {
    console.warn(`Expected ${questionSpecs.count} questions, got ${questions?.length || 0}`);
    return false;
  }

  // Validate each question structure
  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];

    if (!question.id || !question.type || !question.topic || !question.question) {
      console.warn(`Question ${i + 1} missing required fields`);
      return false;
    }

    if (question.type === 'multiple-choice' && (!question.options || !Array.isArray(question.options) || question.options.length < 2)) {
      console.warn(`Question ${i + 1} invalid multiple-choice options`);
      return false;
    }

    if (!question.correctAnswer) {
      console.warn(`Question ${i + 1} missing correct answer`);
      return false;
    }

    if (!question.points || question.points < 1) {
      console.warn(`Question ${i + 1} invalid points value`);
      return false;
    }
  }

  return true;
}

// Analyze mathematics assessment using AI
async function analyzeMathematicsAssessment(answers, level, timeSpent) {
  try {
    console.log('Analyzing mathematics assessment with AI...');

    const analysisPrompt = createMathAnalysisPrompt(answers, level, timeSpent);

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert mathematics assessor specialising in evaluating mathematical proficiency for UK educational placement. Provide detailed analysis using UK mathematical terminology and educational standards."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const analysisText = completion.choices[0].message.content;
    console.log('Raw AI analysis response:', analysisText);

    // Parse the JSON response
    let analysisResult;
    try {
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in AI response');
      }
    } catch (parseError) {
      console.error('Error parsing AI analysis response:', parseError);
      analysisResult = generateFallbackMathAnalysis(answers, level);
    }

    // Validate and normalize the results
    const maxScore = getMathMaxScore(level);
    const passingScore = getMathPassingScore(level);

    analysisResult.score = Math.max(0, Math.min(maxScore, parseInt(analysisResult.score) || 0));
    analysisResult.passed = analysisResult.score >= passingScore;
    analysisResult.level = level;
    analysisResult.maxScore = maxScore;
    analysisResult.passingScore = passingScore;

    console.log('Mathematics assessment analysis completed:', {
      level,
      score: analysisResult.score,
      maxScore,
      passed: analysisResult.passed
    });

    return analysisResult;

  } catch (error) {
    console.error('Error in AI mathematics assessment analysis:', error);
    return generateFallbackMathAnalysis(answers, level);
  }
}

// Store mathematics assessment results in database
async function storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent) {
  try {
    const userCompany = 'Birmingham'; // Default company for student users

    // Prepare level-specific data
    const levelData = {
      completed: true,
      score: analysisResult.score,
      passed: analysisResult.passed,
      timeSpent: timeSpent || 0,
      completedAt: new Date(),
      responses: answers,
      topicBreakdown: analysisResult.topicBreakdown || {}
    };

    // Prepare update data with basic user information for document creation
    const updateData = {
      // Basic user information (in case document doesn't exist)
      userEmail: email,
      userCompany: userCompany,

      // Mathematics assessment data
      mathAssessmentCompleted: true,
      mathCurrentLevel: level,
      mathAssessmentTimestamp: new Date(),
      updatedAt: new Date()
    };

    // Add level-specific data
    switch (level) {
      case 'Entry':
        updateData.mathEntryLevel = levelData;
        break;
      case 'Level1':
        updateData.mathLevel1 = levelData;
        break;
      case 'GCSEPart1':
        updateData.mathGCSEPart1 = levelData;
        break;
      case 'GCSEPart2':
        updateData.mathGCSEPart2 = levelData;
        break;
    }

    // Update overall scores and completion status
    updateData.mathOverallScore = (updateData.mathOverallScore || 0) + analysisResult.score;
    updateData.totalTimeSpentOnMath = (updateData.totalTimeSpentOnMath || 0) + (timeSpent || 0);

    if (analysisResult.passed) {
      updateData.mathHighestLevelCompleted = level;
    }

    // Add feedback and recommendations if provided
    if (analysisResult.feedback) {
      updateData.mathFeedback = analysisResult.feedback;
    }
    if (analysisResult.strengths) {
      updateData.mathStrengths = analysisResult.strengths;
    }
    if (analysisResult.improvements) {
      updateData.mathImprovements = analysisResult.improvements;
    }
    if (analysisResult.placementRecommendation) {
      updateData.mathPlacementRecommendation = analysisResult.placementRecommendation;
    }

    // Ensure company document exists (especially for Birmingham)
    const companyRef = firestore.collection('companies').doc(userCompany);
    const companyDoc = await companyRef.get();

    if (!companyDoc.exists && userCompany === 'Birmingham') {
      console.log('Creating Birmingham company document for mathematics assessment');
      await companyRef.set({
        name: 'Birmingham',
        type: 'student-focused',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        description: 'Auto-created company for student assessments'
      });
    }

    // Create or update user document (using set with merge to handle non-existent documents)
    const userRef = companyRef.collection('users').doc(email);

    console.log('Storing mathematics assessment data for user:', {
      email,
      company: userCompany,
      level,
      operation: 'set with merge'
    });

    // Use set with merge option to create document if it doesn't exist
    await userRef.set(updateData, { merge: true });

    console.log('Mathematics assessment results stored successfully:', {
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed,
      documentCreated: true,
      company: userCompany
    });

  } catch (error) {
    console.error('Error storing mathematics assessment results:', error);
    throw error;
  }
}

// ============================================================================
// MATHEMATICS ASSESSMENT API ENDPOINTS
// ============================================================================

// Performance monitoring endpoint
app.get('/api/math-assessments/performance', (req, res) => {
  try {
    const metrics = getPerformanceMetrics();

    res.status(200).json({
      success: true,
      metrics: {
        ...metrics,
        cacheEntries: Array.from(mathQuestionCache.keys()),
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({
      error: 'Failed to get performance metrics',
      details: error.message
    });
  }
});

// Cache management endpoint
app.post('/api/math-assessments/cache/clear', (req, res) => {
  try {
    const { level, all } = req.body;

    if (all) {
      mathQuestionCache.clear();
      console.log('All mathematics question cache cleared');
      res.status(200).json({
        success: true,
        message: 'All cache cleared',
        cacheSize: mathQuestionCache.size
      });
    } else if (level) {
      const keysToDelete = Array.from(mathQuestionCache.keys()).filter(key => key.startsWith(level));
      keysToDelete.forEach(key => mathQuestionCache.delete(key));
      console.log(`Cache cleared for level: ${level}`);
      res.status(200).json({
        success: true,
        message: `Cache cleared for level: ${level}`,
        keysCleared: keysToDelete.length,
        cacheSize: mathQuestionCache.size
      });
    } else {
      res.status(400).json({
        error: 'Missing required parameter',
        details: 'Specify either "level" or "all": true'
      });
    }
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      error: 'Failed to clear cache',
      details: error.message
    });
  }
});

// Cache warming endpoint (pre-populate cache with questions)
app.post('/api/math-assessments/cache/warm', async (req, res) => {
  try {
    const { levels } = req.body;
    const levelsToWarm = levels || ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    const results = [];

    console.log('Starting cache warming for levels:', levelsToWarm);

    for (const level of levelsToWarm) {
      try {
        const startTime = Date.now();
        const questions = await generateMathematicsQuestions(level, 'adult-learner');
        const generationTime = Date.now() - startTime;

        results.push({
          level,
          success: true,
          questionCount: questions.length,
          generationTime: `${generationTime}ms`,
          cached: true
        });

        console.log(`Cache warmed for ${level}: ${questions.length} questions in ${generationTime}ms`);
      } catch (error) {
        results.push({
          level,
          success: false,
          error: error.message
        });
        console.error(`Failed to warm cache for ${level}:`, error);
      }
    }

    res.status(200).json({
      success: true,
      message: 'Cache warming completed',
      results,
      cacheSize: mathQuestionCache.size
    });
  } catch (error) {
    console.error('Error warming cache:', error);
    res.status(500).json({
      error: 'Failed to warm cache',
      details: error.message
    });
  }
});

// Start Mathematics Assessment - Generate questions for specific level
app.post('/api/math-assessments/start', async (req, res) => {
  console.log('Received mathematics assessment start request');

  try {
    const { level, email, studentLevel } = req.body;

    // Validate required fields
    if (!level || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          level: !level ? 'Missing assessment level' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    // Validate level
    const validLevels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    if (!validLevels.includes(level)) {
      return res.status(400).json({
        error: 'Invalid assessment level',
        details: `Level must be one of: ${validLevels.join(', ')}`
      });
    }

    console.log('Generating mathematics questions for:', {
      email,
      level,
      studentLevel
    });

    // Generate questions using AI
    const questions = await generateMathematicsQuestions(level, studentLevel);

    // Create assessment session
    const assessmentId = `math_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('Mathematics assessment started:', {
      assessmentId,
      email,
      level,
      questionCount: questions.length
    });

    res.status(200).json({
      assessmentId,
      level,
      questions,
      timeLimit: getMathTimeLimit(level),
      passingScore: getMathPassingScore(level),
      maxScore: getMathMaxScore(level)
    });

  } catch (error) {
    console.error('Error starting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to start mathematics assessment',
      details: error.message
    });
  }
});

// Submit Mathematics Assessment - Process answers and generate results
app.post('/api/math-assessments/:id/submit', async (req, res) => {
  console.log('Received mathematics assessment submission');

  try {
    const { id: assessmentId } = req.params;
    const { answers, email, level, timeSpent } = req.body;

    // Validate required fields
    if (!answers || !email || !level) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          answers: !answers ? 'Missing answers' : null,
          email: !email ? 'Missing email' : null,
          level: !level ? 'Missing level' : null
        }
      });
    }

    console.log('Processing mathematics assessment submission:', {
      assessmentId,
      email,
      level,
      answerCount: answers.length,
      timeSpent: timeSpent || 'unknown'
    });

    // Analyze answers with AI
    const analysisResult = await analyzeMathematicsAssessment(answers, level, timeSpent);

    // Store results in database
    await storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent);

    console.log('Mathematics assessment completed:', {
      assessmentId,
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error submitting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to submit mathematics assessment',
      details: error.message
    });
  }
});

// Get Mathematics Assessment Report
app.get('/api/math-assessments/:email/report', async (req, res) => {
  try {
    const { email } = req.params;
    const { company = 'Birmingham' } = req.query;

    const userDoc = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .doc(email)
      .get();

    if (!userDoc.exists) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    const userData = userDoc.data();

    const reportData = {
      userEmail: email,
      name: userData.name || 'Unknown',
      assessmentCompleted: userData.mathAssessmentCompleted || false,
      currentLevel: userData.mathCurrentLevel || null,
      overallScore: userData.mathOverallScore || 0,
      highestLevelCompleted: userData.mathHighestLevelCompleted || null,
      completedAt: userData.mathAssessmentTimestamp || null,
      totalTimeSpent: userData.totalTimeSpentOnMath || 0,

      // Level-specific data
      entryLevel: userData.mathEntryLevel || null,
      level1: userData.mathLevel1 || null,
      gcsePart1: userData.mathGCSEPart1 || null,
      gcsePart2: userData.mathGCSEPart2 || null,

      // Analysis results
      feedback: userData.mathFeedback || {},
      strengths: userData.mathStrengths || [],
      improvements: userData.mathImprovements || [],
      placementRecommendation: userData.mathPlacementRecommendation || {}
    };

    res.json({
      success: true,
      data: reportData
    });

  } catch (error) {
    console.error('Error fetching mathematics assessment report:', error);
    res.status(500).json({
      error: 'Failed to fetch mathematics assessment report',
      details: error.message
    });
  }
});

// ============================================================================
// MATHEMATICS ASSESSMENT ADMIN DASHBOARD API ENDPOINTS
// ============================================================================

// Get mathematics assessment analytics and statistics
app.get('/api/admin/math-analytics', async (req, res) => {
  try {
    const { company = 'Birmingham' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('mathAssessmentCompleted', '==', true)
      .get();

    const analytics = {
      totalAssessments: 0,
      levelDistribution: {
        'Entry': 0,
        'Level1': 0,
        'GCSEPart1': 0,
        'GCSEPart2': 0
      },
      completionRates: {
        'Entry': { completed: 0, passed: 0 },
        'Level1': { completed: 0, passed: 0 },
        'GCSEPart1': { completed: 0, passed: 0 },
        'GCSEPart2': { completed: 0, passed: 0 }
      },
      averageScores: {
        overall: 0,
        byLevel: {}
      },
      averageDuration: 0,
      topicPerformance: {
        arithmetic: 0,
        algebra: 0,
        geometry: 0,
        statistics: 0,
        problemSolving: 0
      }
    };

    let totalOverallScore = 0;
    let totalDuration = 0;
    let durationsCount = 0;

    snapshot.forEach(doc => {
      const userData = doc.data();
      analytics.totalAssessments++;

      // Track highest level completed
      const highestLevel = userData.mathHighestLevelCompleted;
      if (highestLevel && analytics.levelDistribution[highestLevel] !== undefined) {
        analytics.levelDistribution[highestLevel]++;
      }

      // Track completion and passing rates for each level
      ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'].forEach(level => {
        const levelKey = level === 'Entry' ? 'mathEntryLevel' :
                        level === 'Level1' ? 'mathLevel1' :
                        level === 'GCSEPart1' ? 'mathGCSEPart1' : 'mathGCSEPart2';

        const levelData = userData[levelKey];
        if (levelData && levelData.completed) {
          analytics.completionRates[level].completed++;
          if (levelData.passed) {
            analytics.completionRates[level].passed++;
          }
        }
      });

      // Calculate average scores
      if (userData.mathOverallScore) {
        totalOverallScore += userData.mathOverallScore;
      }

      // Calculate average duration
      if (userData.totalTimeSpentOnMath) {
        totalDuration += userData.totalTimeSpentOnMath;
        durationsCount++;
      }
    });

    // Calculate averages
    if (analytics.totalAssessments > 0) {
      analytics.averageScores.overall = Math.round(totalOverallScore / analytics.totalAssessments);
    }

    if (durationsCount > 0) {
      analytics.averageDuration = Math.round(totalDuration / durationsCount);
    }

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Error fetching mathematics analytics:', error);
    res.status(500).json({
      error: 'Failed to fetch mathematics analytics',
      details: error.message
    });
  }
});

// Get all mathematics assessment responses with filtering
app.get('/api/admin/math-responses', async (req, res) => {
  try {
    const {
      company = 'Birmingham',
      level,
      completedAfter,
      completedBefore,
      limit = 50,
      offset = 0
    } = req.query;

    let query = firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('mathAssessmentCompleted', '==', true);

    // Apply filters
    if (level) {
      query = query.where('mathHighestLevelCompleted', '==', level);
    }

    if (completedAfter) {
      query = query.where('mathAssessmentTimestamp', '>=', new Date(completedAfter));
    }

    if (completedBefore) {
      query = query.where('mathAssessmentTimestamp', '<=', new Date(completedBefore));
    }

    // Apply pagination
    query = query.limit(parseInt(limit)).offset(parseInt(offset));

    const snapshot = await query.get();
    const responses = [];

    snapshot.forEach(doc => {
      const userData = doc.data();
      responses.push({
        userEmail: doc.id,
        name: userData.name || 'Unknown',
        currentLevel: userData.mathCurrentLevel,
        highestLevelCompleted: userData.mathHighestLevelCompleted,
        overallScore: userData.mathOverallScore || 0,
        completedAt: userData.mathAssessmentTimestamp,
        totalTimeSpent: userData.totalTimeSpentOnMath || 0,

        // Level-specific data
        entryLevel: userData.mathEntryLevel,
        level1: userData.mathLevel1,
        gcsePart1: userData.mathGCSEPart1,
        gcsePart2: userData.mathGCSEPart2,

        // Feedback data
        feedback: userData.mathFeedback || {},
        strengths: userData.mathStrengths || [],
        improvements: userData.mathImprovements || [],
        placementRecommendation: userData.mathPlacementRecommendation || {}
      });
    });

    res.json({
      success: true,
      data: responses,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: responses.length
      }
    });

  } catch (error) {
    console.error('Error fetching mathematics responses:', error);
    res.status(500).json({
      error: 'Failed to fetch mathematics responses',
      details: error.message
    });
  }
});

// Get detailed mathematics assessment data for a specific user
app.get('/api/admin/math-responses/:email', async (req, res) => {
  try {
    const { email } = req.params;
    const { company = 'Birmingham' } = req.query;

    const userDoc = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .doc(email)
      .get();

    if (!userDoc.exists) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    const userData = userDoc.data();

    const responseData = {
      userEmail: email,
      name: userData.name || 'Unknown',
      assessmentCompleted: userData.mathAssessmentCompleted || false,
      currentLevel: userData.mathCurrentLevel || null,
      overallScore: userData.mathOverallScore || 0,
      highestLevelCompleted: userData.mathHighestLevelCompleted || null,
      completedAt: userData.mathAssessmentTimestamp || null,
      totalTimeSpent: userData.totalTimeSpentOnMath || 0,

      // Detailed level data
      entryLevel: userData.mathEntryLevel || null,
      level1: userData.mathLevel1 || null,
      gcsePart1: userData.mathGCSEPart1 || null,
      gcsePart2: userData.mathGCSEPart2 || null,

      // Analysis results
      feedback: userData.mathFeedback || {},
      strengths: userData.mathStrengths || [],
      improvements: userData.mathImprovements || [],
      placementRecommendation: userData.mathPlacementRecommendation || {}
    };

    res.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching user mathematics assessment data:', error);
    res.status(500).json({
      error: 'Failed to fetch user mathematics assessment data',
      details: error.message
    });
  }
});

app.listen(port, async () => {
  console.log(`Server is running on http://localhost:${port}`);

  // Initialize learning path data at startup
  await initializeLearningPathData();

  // Initialize Birmingham company for student-focused version
  await initializeBirminghamCompany();
});