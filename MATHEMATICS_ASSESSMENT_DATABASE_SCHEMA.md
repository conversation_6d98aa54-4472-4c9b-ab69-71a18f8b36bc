# Mathematics Assessment Database Schema Documentation

## Overview
This document outlines the complete database structure for the mathematics assessment system, designed to mirror the English assessment structure while accommodating mathematics-specific requirements including progressive level assessment, topic-based scoring, and comprehensive analytics.

## Database Structure

### Collection Path
```
companies/{companyId}/users/{userEmail}
```

### Complete Mathematics Assessment Fields

#### Core Assessment Fields
```javascript
{
  // Basic assessment completion tracking
  mathAssessmentCompleted: boolean,
  mathCurrentLevel: string,              // "Entry", "Level1", "GCSEPart1", "GCSEPart2"
  mathOverallScore: number,              // Total points across all completed levels
  mathHighestLevelCompleted: string,     // Highest level successfully completed
  mathAssessmentTimestamp: timestamp,    // When assessment was last updated
  totalTimeSpentOnMath: number,         // Total time in seconds across all levels
  updatedAt: timestamp                  // Last update timestamp
}
```

#### Level-Specific Assessment Fields
```javascript
{
  // Entry Level Assessment (30 minutes, 22 questions, ≥24 points to pass)
  mathEntryLevel: {
    completed: boolean,
    score: number,                      // 0-44 scale (2 points per question)
    passed: boolean,                    // score >= 24
    timeSpent: number,                  // Time in seconds
    completedAt: timestamp,
    responses: object[],                // Array of question responses
    topicBreakdown: {
      arithmetic: number,               // Score in arithmetic questions
      fractions: number,                // Score in fractions questions
      percentages: number,              // Score in percentages questions
      basicAlgebra: number,             // Score in basic algebra questions
      measurement: number,              // Score in measurement questions
      dataHandling: number              // Score in data handling questions
    }
  },
  
  // Level 1 Assessment (30 minutes, 12-13 questions, ≥16 points to pass)
  mathLevel1: {
    completed: boolean,
    score: number,                      // 0-26 scale (2 points per question)
    passed: boolean,                    // score >= 16
    timeSpent: number,
    completedAt: timestamp,
    responses: object[],
    topicBreakdown: {
      advancedArithmetic: number,
      fractionsDecimals: number,
      percentagesRatio: number,
      algebraicExpressions: number,
      geometry: number,
      statistics: number
    }
  },
  
  // GCSE Part 1 - Non-calculator (15 minutes, 6-7 questions, ≥5/10 points to pass)
  mathGCSEPart1: {
    completed: boolean,
    score: number,                      // 0-10 scale
    passed: boolean,                    // score >= 5
    timeSpent: number,
    completedAt: timestamp,
    responses: object[],
    topicBreakdown: {
      numberOperations: number,
      algebraicManipulation: number,
      geometricReasoning: number,
      fractionalCalculations: number
    }
  },
  
  // GCSE Part 2 - Calculator allowed (20 minutes, 9-10 questions, combined threshold varies)
  mathGCSEPart2: {
    completed: boolean,
    score: number,                      // 0-20 scale (2 points per question)
    passed: boolean,                    // Combined GCSE threshold
    timeSpent: number,
    completedAt: timestamp,
    responses: object[],
    topicBreakdown: {
      complexCalculations: number,
      statisticalAnalysis: number,
      trigonometry: number,
      advancedAlgebra: number,
      problemSolving: number
    }
  }
}
```

#### Enhanced Feedback Fields
```javascript
{
  // Detailed AI-generated feedback object
  mathFeedback: {
    numericalSkills: string,           // Assessment of arithmetic and calculation abilities
    algebraicThinking: string,         // Evaluation of algebraic reasoning and manipulation
    problemSolving: string,            // Analysis of word problem and application skills
    geometricReasoning: string,        // Assessment of spatial and geometric understanding
    dataHandling: string,              // Evaluation of statistics and data interpretation
    overall: string                    // Comprehensive summary and next steps
  },
  
  // Array of identified mathematical strengths
  mathStrengths: string[],
  
  // Array of improvement areas
  mathImprovements: string[],
  
  // Placement recommendations based on performance
  mathPlacementRecommendation: {
    level: string,                     // "Entry Support", "Level 1", "GCSE Foundation", "GCSE Higher"
    reasoning: string,                 // Explanation for the recommendation
    nextSteps: string[],              // Specific learning recommendations
    courseRecommendations: string[]    // Suggested courses or resources
  }
}
```

## Field Specifications

### 1. mathFeedback (Object)
**Type**: `Object`
**Required**: Yes (with fallback defaults)
**Structure**:
```javascript
{
  numericalSkills: string,      // 50-200 characters typical
  algebraicThinking: string,    // 50-200 characters typical  
  problemSolving: string,       // 50-200 characters typical
  geometricReasoning: string,   // 50-200 characters typical
  dataHandling: string,         // 50-200 characters typical
  overall: string               // 100-300 characters typical
}
```

### 2. mathStrengths (Array)
**Type**: `Array<string>`
**Required**: Yes (with fallback defaults)
**Typical Length**: 2-5 items
**Item Length**: 20-80 characters each

**Sample Data**:
```javascript
mathStrengths: [
  "Strong arithmetic calculation skills",
  "Good understanding of fractions",
  "Effective problem-solving approach",
  "Clear algebraic reasoning",
  "Accurate data interpretation"
]
```

### 3. mathImprovements (Array)
**Type**: `Array<string>`
**Required**: Yes (with fallback defaults)
**Typical Length**: 2-5 items
**Item Length**: 20-80 characters each

**Sample Data**:
```javascript
mathImprovements: [
  "Practice percentage calculations",
  "Strengthen geometric reasoning",
  "Work on complex word problems",
  "Review algebraic manipulation",
  "Focus on statistical concepts"
]
```

## Progressive Assessment Logic

### Level Progression Rules
```javascript
// Entry Level → Level 1
if (mathEntryLevel.passed && mathEntryLevel.score >= 24) {
  // Unlock Level 1 assessment
}

// Level 1 → GCSE Part 1
if (mathLevel1.passed && mathLevel1.score >= 16) {
  // Unlock GCSE Part 1 assessment
}

// GCSE Part 1 → GCSE Part 2
if (mathGCSEPart1.passed && mathGCSEPart1.score >= 5) {
  // Unlock GCSE Part 2 assessment
}

// Final GCSE Assessment
const combinedGCSEScore = mathGCSEPart1.score + mathGCSEPart2.score;
const gcsePassed = combinedGCSEScore >= 15; // Combined threshold
```

### Placement Recommendation Logic
```javascript
function determinePlacement(assessmentData) {
  if (assessmentData.mathGCSEPart2.completed && assessmentData.mathGCSEPart2.passed) {
    return "GCSE Higher";
  } else if (assessmentData.mathGCSEPart1.completed && assessmentData.mathGCSEPart1.passed) {
    return "GCSE Foundation";
  } else if (assessmentData.mathLevel1.completed && assessmentData.mathLevel1.passed) {
    return "Level 1";
  } else {
    return "Entry Support";
  }
}
```

## Data Validation Rules

### Score Validation
```javascript
// Entry Level validation
if (entryScore < 0 || entryScore > 44) throw new Error('Invalid Entry Level score');

// Level 1 validation  
if (level1Score < 0 || level1Score > 26) throw new Error('Invalid Level 1 score');

// GCSE Part 1 validation
if (gcsePart1Score < 0 || gcsePart1Score > 10) throw new Error('Invalid GCSE Part 1 score');

// GCSE Part 2 validation
if (gcsePart2Score < 0 || gcsePart2Score > 20) throw new Error('Invalid GCSE Part 2 score');
```

### Required Field Fallbacks
```javascript
// Fallback values if AI analysis fails
const fallbackMathFeedback = {
  numericalSkills: 'Numerical skills assessed',
  algebraicThinking: 'Algebraic reasoning evaluated',
  problemSolving: 'Problem-solving abilities reviewed',
  geometricReasoning: 'Geometric understanding assessed',
  dataHandling: 'Data handling skills evaluated',
  overall: 'Mathematics proficiency assessment completed'
};

const fallbackMathStrengths = ['Completed the mathematics assessment'];
const fallbackMathImprovements = ['Continue practicing mathematics'];
```

## Performance Considerations

### Document Size
- Mathematics assessment fields add approximately 2-4KB per user document
- Well within Firestore document size limits (1MB)
- Indexing recommended for completion status and level fields

### Query Optimization
```javascript
// Efficient query for assessment status
const query = userRef.where('mathAssessmentCompleted', '==', true)
                    .where('mathHighestLevelCompleted', '==', 'GCSEPart2');
```

## Security Rules

### Firestore Security Rules Example
```javascript
// Allow users to read their own mathematics assessment data
match /companies/{companyId}/users/{userId} {
  allow read: if request.auth != null && request.auth.token.email == userId;
  allow write: if request.auth != null && 
               request.auth.token.email == userId &&
               validateMathAssessmentData(request.resource.data);
}

function validateMathAssessmentData(data) {
  return data.mathOverallScore is number &&
         data.mathOverallScore >= 0 &&
         data.mathCurrentLevel is string &&
         data.mathCurrentLevel in ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
}
```
