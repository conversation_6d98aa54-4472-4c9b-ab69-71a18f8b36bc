<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.8/lottie.min.js"></script>

    <!-- MathQuill for equation editing -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
    <title>Mathematics Assessment - Skills Gap Analyzer</title>
</head>

<body class="bg-cover bg-center h-screen w-screen overflow-hidden bg-fixed flex items-center justify-center">
    <!-- Header with progress information -->
    <div id="header" class="fixed top-0 left-0 right-0 p-5 bg-blue-900 flex items-center justify-between h-12 text-white text-sm shadow-md hidden">
        <div class="header-texts flex justify-between w-full items-center">
            <h2 class="text-xs">Mathematics Assessment</h2>
            <h2 class="text-xs">Level: <span id="current-level">Entry</span></h2>
            <h2 class="text-xs">Question <span id="current-question">1</span> of <span id="total-questions">22</span></h2>
            <h2 class="text-xs">Time: <span id="timer-display">30:00</span></h2>
        </div>
    </div>

    <!-- User Information Form -->
    <div id="user-form-container" class="hidden">
        <div class="modern-form-container">
            <div class="form-header">
                <h2>Mathematics Assessment</h2>
                <p>Please provide your information to start your mathematics assessment</p>
            </div>

            <div class="form-content">
                <form id="user-form" class="modern-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">Personal Information</h3>

                        <div class="input-grid">
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="first-name"
                                    name="first-name"
                                    required
                                    class="modern-input"
                                    placeholder="First Name"
                                >
                            </div>

                            <div class="input-group">
                                <input
                                    type="text"
                                    id="last-name"
                                    name="last-name"
                                    required
                                    class="modern-input"
                                    placeholder="Last Name"
                                >
                            </div>
                        </div>

                        <div class="input-group">
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                class="modern-input"
                                placeholder="Email Address"
                            >
                        </div>
                    </div>

                    <!-- Assessment Level Selection -->
                    <div class="form-section">
                        <h3 class="section-title">Assessment Level</h3>
                        <p class="section-description">Select the mathematics level you'd like to assess</p>

                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Entry" checked>
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Entry Level</span>
                                    <span class="radio-description">Basic arithmetic, fractions, percentages (30 min, 22 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Level1">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Level 1</span>
                                    <span class="radio-description">Advanced arithmetic, algebra, geometry (30 min, 13 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="GCSEPart1">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">GCSE Part 1 (Non-calculator)</span>
                                    <span class="radio-description">Number operations, algebra (15 min, 7 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="GCSEPart2">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">GCSE Part 2 (Calculator)</span>
                                    <span class="radio-description">Complex calculations, statistics, trigonometry (20 min, 10 questions)</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Student Level -->
                    <div class="form-section">
                        <h3 class="section-title">Student Level</h3>
                        <div class="input-group">
                            <select id="student-level" name="student-level" class="modern-select" required>
                                <option value="">Select your level</option>
                                <option value="adult-learner">Adult Learner</option>
                                <option value="returning-student">Returning Student</option>
                                <option value="school-leaver">School Leaver</option>
                                <option value="career-changer">Career Changer</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <div class="form-footer">
                <div class="form-actions">
                    <button type="submit" form="user-form" id="submit-form" class="modern-submit-btn">
                        <span class="btn-text">Start Mathematics Assessment</span>
                        <span class="btn-icon">→</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mathematics Assessment Container -->
    <div id="math-assessment-container" class="hidden">
        <!-- Assessment Instructions -->
        <div id="assessment-instructions" class="assessment-screen">
            <div class="instruction-container">
                <h2 class="instruction-title">Mathematics Assessment Instructions</h2>
                <div class="instruction-content">
                    <div class="instruction-item">
                        <span class="instruction-icon">📝</span>
                        <p>You will have <span id="time-limit-display">30 minutes</span> to complete <span id="question-count-display">22 questions</span></p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">🎯</span>
                        <p>You need <span id="passing-score-display">24 points</span> to pass this level</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">⏰</span>
                        <p>The timer will start when you click "Begin Assessment"</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">💡</span>
                        <p>Answer all questions to the best of your ability</p>
                    </div>
                </div>
                <button id="begin-assessment-btn" class="modern-submit-btn">
                    <span class="btn-text">Begin Assessment</span>
                    <span class="btn-icon">▶</span>
                </button>
            </div>
        </div>

        <!-- Assessment Questions -->
        <div id="assessment-questions" class="assessment-screen hidden">
            <div class="question-container">
                <div class="question-header">
                    <div class="question-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <span class="progress-text">Question <span id="question-number">1</span> of <span id="total-question-count">22</span></span>
                    </div>
                    <div class="timer-container">
                        <span id="timer">30:00</span>
                    </div>
                </div>

                <div class="question-content">
                    <div class="question-topic">
                        <span id="question-topic">Arithmetic</span>
                    </div>
                    <h3 id="question-text" class="question-text">Loading question...</h3>
                    
                    <!-- Multiple Choice Options -->
                    <div id="multiple-choice-options" class="answer-options hidden">
                        <button class="option-btn" data-option="0"></button>
                        <button class="option-btn" data-option="1"></button>
                        <button class="option-btn" data-option="2"></button>
                        <button class="option-btn" data-option="3"></button>
                    </div>

                    <!-- Numeric Input -->
                    <div id="numeric-input" class="answer-input hidden">
                        <input type="text" id="numeric-answer" class="numeric-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter a number (e.g., 42 or 3.14)</p>
                    </div>

                    <!-- Short Answer Input -->
                    <div id="short-answer-input" class="answer-input hidden">
                        <input type="text" id="short-answer" class="text-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter your mathematical expression</p>
                    </div>

                    <!-- Number Line Slider -->
                    <div id="number-line-input" class="answer-input hidden">
                        <div class="number-line-container">
                            <div class="number-line-instruction">
                                <p>Drag the marker to the correct position on the number line</p>
                            </div>
                            <div id="number-line-widget" class="number-line-widget">
                                <div class="number-line-track"></div>
                                <div class="number-line-markers"></div>
                                <div class="number-line-slider" draggable="true"></div>
                                <div class="number-line-value-display"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Drag and Drop Puzzle -->
                    <div id="drag-drop-input" class="answer-input hidden">
                        <div class="drag-drop-container">
                            <div class="drag-drop-instruction">
                                <p>Drag the mathematical elements to complete the equation or solve the problem</p>
                            </div>
                            <div class="drag-elements-bank" id="drag-elements-bank">
                                <!-- Draggable elements will be populated here -->
                            </div>
                            <div class="drop-zones-container" id="drop-zones-container">
                                <!-- Drop zones will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Equation Builder with MathQuill -->
                    <div id="equation-builder-input" class="answer-input hidden">
                        <div class="equation-builder-container">
                            <div class="equation-builder-instruction">
                                <p>Use the toolbar to build your mathematical expression</p>
                            </div>
                            <div class="equation-toolbar" id="equation-toolbar">
                                <button class="toolbar-btn" data-symbol="+">+</button>
                                <button class="toolbar-btn" data-symbol="-">−</button>
                                <button class="toolbar-btn" data-symbol="*">×</button>
                                <button class="toolbar-btn" data-symbol="/">/</button>
                                <button class="toolbar-btn" data-symbol="^">x²</button>
                                <button class="toolbar-btn" data-symbol="sqrt">√</button>
                                <button class="toolbar-btn" data-symbol="frac">½</button>
                                <button class="toolbar-btn" data-symbol="(">(</button>
                                <button class="toolbar-btn" data-symbol=")">)</button>
                            </div>
                            <div id="equation-editor" class="equation-editor"></div>
                        </div>
                    </div>

                    <!-- Step-by-Step Worked Answer -->
                    <div id="step-by-step-input" class="answer-input hidden">
                        <div class="step-by-step-container">
                            <div class="step-by-step-instruction">
                                <p>Work through this problem step by step</p>
                            </div>
                            <div id="steps-container" class="steps-container">
                                <!-- Steps will be populated dynamically -->
                            </div>
                            <div class="step-navigation">
                                <button id="prev-step-btn" class="step-nav-btn" disabled>Previous</button>
                                <span id="step-indicator" class="step-indicator">Step 1 of 3</span>
                                <button id="next-step-btn" class="step-nav-btn">Next</button>
                            </div>
                        </div>
                    </div>

                    <!-- Interactive Charts and Coordinate Grids -->
                    <div id="coordinate-grid-input" class="answer-input hidden">
                        <div class="coordinate-grid-container">
                            <div class="coordinate-grid-instruction">
                                <p>Click or drag to plot points, draw lines, or manipulate graphical elements</p>
                            </div>
                            <div id="coordinate-grid" class="coordinate-grid">
                                <svg id="grid-svg" class="grid-svg" viewBox="0 0 400 400">
                                    <!-- Grid lines and interactive elements will be added via JavaScript -->
                                </svg>
                            </div>
                            <div class="grid-tools">
                                <button class="grid-tool-btn active" data-tool="point">Plot Point</button>
                                <button class="grid-tool-btn" data-tool="line">Draw Line</button>
                                <button class="grid-tool-btn" data-tool="clear">Clear</button>
                            </div>
                        </div>
                    </div>

                    <!-- Visual Manipulatives -->
                    <div id="visual-manipulatives-input" class="answer-input hidden">
                        <div class="manipulatives-container">
                            <div class="manipulatives-instruction">
                                <p>Drag and manipulate the objects to solve the problem</p>
                            </div>
                            <div id="manipulatives-workspace" class="manipulatives-workspace">
                                <!-- Interactive objects will be populated here -->
                            </div>
                            <div class="manipulatives-toolbox" id="manipulatives-toolbox">
                                <!-- Tools and objects will be populated based on question type -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="question-actions">
                    <button id="skip-question-btn" class="skip-btn">Skip Question</button>
                    <button id="next-question-btn" class="next-btn" disabled>Next Question</button>
                </div>
            </div>
        </div>

        <!-- Assessment Results -->
        <div id="assessment-results" class="assessment-screen hidden">
            <div class="results-container">
                <div class="results-header">
                    <h2 class="results-title">Assessment Complete!</h2>
                    <div class="results-score">
                        <span class="score-label">Your Score:</span>
                        <span id="final-score" class="score-value">0</span>
                        <span class="score-total">/ <span id="max-score">44</span></span>
                    </div>
                    <div class="result-status">
                        <div id="pass-status" class="status-badge">
                            <span id="status-text">Assessment Complete</span>
                        </div>
                    </div>
                </div>

                <div class="results-content">
                    <div class="feedback-section">
                        <h3>Your Performance</h3>
                        <div id="topic-breakdown" class="topic-breakdown">
                            <!-- Topic performance will be populated here -->
                        </div>
                    </div>

                    <div class="recommendations-section">
                        <h3>Next Steps</h3>
                        <div id="recommendations" class="recommendations">
                            <!-- Recommendations will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="results-actions">
                    <button id="view-detailed-report-btn" class="modern-submit-btn">
                        <span class="btn-text">View Detailed Report</span>
                        <span class="btn-icon">📊</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="assessment-loading" class="assessment-screen hidden">
            <div class="loading-container">
                <div class="loading-animation">
                    <div class="spinner"></div>
                </div>
                <h3 class="loading-title">Processing Your Assessment</h3>
                <p class="loading-text">Analyzing your responses and generating your mathematics report...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="validationFunctions.js"></script>
    <script src="mathInteractiveComponents.js"></script>
    <script src="mathPerformanceOptimizer.js"></script>
    <script src="mathAssessment.js"></script>
    <script>
        // Initialize the mathematics assessment when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all containers are hidden initially
            document.getElementById('math-assessment-container').classList.add('hidden');
            document.getElementById('header').classList.add('hidden');

            // Show only the user form initially
            document.getElementById('user-form-container').classList.remove('hidden');

            // Initialize the mathematics assessment system
            if (typeof MathAssessment !== 'undefined') {
                window.mathAssessment = new MathAssessment();
                window.mathAssessment.init();
            } else {
                console.error('MathAssessment class not found');
            }
        });
    </script>
</body>
</html>
