/**
 * Mathematics Assessment Module
 * Handles the mathematics assessment flow for student users
 */

class MathAssessment {
  constructor() {
    this.currentLevel = 'Entry';
    this.timeLimit = 30 * 60; // Default 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    
    // Assessment data
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.assessmentId = null;
    
    // Assessment metadata
    this.assessmentStartTime = null;
    this.questionStartTimes = [];
    
    // Level specifications
    this.levelSpecs = {
      'Entry': { timeLimit: 30 * 60, questionCount: 22, passingScore: 24, maxScore: 44 },
      'Level1': { timeLimit: 30 * 60, questionCount: 13, passingScore: 16, maxScore: 26 },
      'GCSEPart1': { timeLimit: 15 * 60, questionCount: 7, passingScore: 5, maxScore: 10 },
      'GCSEPart2': { timeLimit: 20 * 60, questionCount: 10, passingScore: 8, maxScore: 20 }
    };
  }

  /**
   * Initialize the mathematics assessment
   */
  async init() {
    this.setupEventListeners();
    console.log('Mathematics assessment initialized');

    // Auto-initialize if page is loaded directly
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.autoInit());
    } else {
      this.autoInit();
    }
  }

  /**
   * Auto-initialize the assessment interface
   */
  autoInit() {
    // Ensure proper initial state
    this.resetToInitialState();
  }

  /**
   * Reset to initial state - only user form visible
   */
  resetToInitialState() {
    // Hide all assessment containers
    const mathContainer = document.getElementById('math-assessment-container');
    const header = document.getElementById('header');

    if (mathContainer) mathContainer.classList.add('hidden');
    if (header) header.classList.add('hidden');

    // Hide all assessment screens
    this.hideAllAssessmentScreens();

    // Show only the user form
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.remove('hidden');
    }
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    // Form submission
    const userForm = document.getElementById('user-form');
    if (userForm) {
      userForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    // Begin assessment button
    const beginBtn = document.getElementById('begin-assessment-btn');
    if (beginBtn) {
      beginBtn.addEventListener('click', () => this.beginAssessment());
    }

    // Next question button
    const nextBtn = document.getElementById('next-question-btn');
    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.nextQuestion());
    }

    // Skip question button
    const skipBtn = document.getElementById('skip-question-btn');
    if (skipBtn) {
      skipBtn.addEventListener('click', () => this.skipQuestion());
    }

    // Answer option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
      btn.addEventListener('click', (e) => this.selectOption(e));
    });

    // Numeric input
    const numericInput = document.getElementById('numeric-answer');
    if (numericInput) {
      numericInput.addEventListener('input', () => this.handleNumericInput());
    }

    // Short answer input
    const shortAnswerInput = document.getElementById('short-answer');
    if (shortAnswerInput) {
      shortAnswerInput.addEventListener('input', () => this.handleShortAnswerInput());
    }
  }

  /**
   * Handle form submission to start assessment
   */
  async handleFormSubmit(e) {
    e.preventDefault();
    
    try {
      // Get form data
      const formData = new FormData(e.target);
      const firstName = formData.get('first-name');
      const lastName = formData.get('last-name');
      const email = formData.get('email');
      const assessmentLevel = formData.get('assessment-level');
      const studentLevel = formData.get('student-level');

      // Validate required fields
      if (!firstName || !lastName || !email || !assessmentLevel || !studentLevel) {
        alert('Please fill in all required fields');
        return;
      }

      // Store user data
      this.userData = {
        firstName,
        lastName,
        email,
        assessmentLevel,
        studentLevel,
        name: `${firstName} ${lastName}`
      };

      this.currentLevel = assessmentLevel;
      
      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Hide form and show instructions
      this.hideUserForm();
      this.showInstructions();

    } catch (error) {
      console.error('Error handling form submission:', error);
      alert('An error occurred. Please try again.');
    }
  }

  /**
   * Show assessment instructions
   */
  showInstructions() {
    const specs = this.levelSpecs[this.currentLevel];

    // Ensure user form is completely hidden
    this.hideUserForm();

    // Hide all assessment screens first
    this.hideAllAssessmentScreens();

    // Update instruction display
    document.getElementById('time-limit-display').textContent = `${specs.timeLimit / 60} minutes`;
    document.getElementById('question-count-display').textContent = specs.questionCount;
    document.getElementById('passing-score-display').textContent = `${specs.passingScore} points`;

    // Update header
    document.getElementById('current-level').textContent = this.currentLevel;
    document.getElementById('total-questions').textContent = specs.questionCount;

    // Show only the math assessment container and instructions
    document.getElementById('math-assessment-container').classList.remove('hidden');
    document.getElementById('assessment-instructions').classList.remove('hidden');
    document.getElementById('header').classList.remove('hidden');
  }

  /**
   * Begin the assessment
   */
  async beginAssessment() {
    try {
      // Show loading
      this.showLoading('Generating your mathematics questions...');
      
      // Start assessment
      await this.startAssessment();
      
      // Hide loading and show questions
      this.hideLoading();
      this.showQuestions();
      
    } catch (error) {
      console.error('Error beginning assessment:', error);
      this.hideLoading();
      alert('Failed to start assessment. Please try again.');
    }
  }

  /**
   * Start the assessment by fetching questions
   */
  async startAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const response = await fetch(`${baseUrl}/api/math-assessments/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: this.currentLevel,
          email: this.userData.email,
          studentLevel: this.userData.studentLevel
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start assessment: ${response.status}`);
      }

      const data = await response.json();
      
      this.assessmentId = data.assessmentId;
      this.questions = data.questions;
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentStartTime = new Date();
      
      // Start timer
      this.startTimer();
      
      console.log('Assessment started:', {
        assessmentId: this.assessmentId,
        level: this.currentLevel,
        questionCount: this.questions.length
      });

    } catch (error) {
      console.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Hide all assessment screens
   */
  hideAllAssessmentScreens() {
    document.getElementById('assessment-instructions').classList.add('hidden');
    document.getElementById('assessment-questions').classList.add('hidden');
    document.getElementById('assessment-results').classList.add('hidden');
    document.getElementById('assessment-loading').classList.add('hidden');
  }

  /**
   * Show the questions interface
   */
  showQuestions() {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only questions container
    document.getElementById('assessment-questions').classList.remove('hidden');

    // Load first question
    this.loadCurrentQuestion();
  }

  /**
   * Load the current question
   */
  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeAssessment();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];
    const specs = this.levelSpecs[this.currentLevel];
    
    // Update progress
    document.getElementById('question-number').textContent = this.currentQuestionIndex + 1;
    document.getElementById('total-question-count').textContent = specs.questionCount;
    document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
    
    const progressPercent = ((this.currentQuestionIndex + 1) / specs.questionCount) * 100;
    document.getElementById('progress-fill').style.width = `${progressPercent}%`;
    
    // Update question content
    document.getElementById('question-topic').textContent = question.topic || 'Mathematics';
    document.getElementById('question-text').textContent = question.question;
    
    // Hide all answer types
    this.hideAllAnswerTypes();
    
    // Show appropriate answer type
    if (question.type === 'multiple-choice') {
      this.showMultipleChoice(question);
    } else if (question.type === 'numeric') {
      this.showNumericInput();
    } else if (question.type === 'short-answer') {
      this.showShortAnswerInput();
    }
    
    // Reset next button
    document.getElementById('next-question-btn').disabled = true;
    
    // Record question start time
    this.questionStartTimes[this.currentQuestionIndex] = new Date();
  }

  /**
   * Hide all answer input types
   */
  hideAllAnswerTypes() {
    document.getElementById('multiple-choice-options').classList.add('hidden');
    document.getElementById('numeric-input').classList.add('hidden');
    document.getElementById('short-answer-input').classList.add('hidden');
  }

  /**
   * Show multiple choice options
   */
  showMultipleChoice(question) {
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');
    
    buttons.forEach((btn, index) => {
      if (index < question.options.length) {
        btn.textContent = question.options[index];
        btn.style.display = 'block';
        btn.classList.remove('selected');
      } else {
        btn.style.display = 'none';
      }
    });
    
    container.classList.remove('hidden');
  }

  /**
   * Show numeric input
   */
  showNumericInput() {
    document.getElementById('numeric-input').classList.remove('hidden');
    document.getElementById('numeric-answer').value = '';
    document.getElementById('numeric-answer').focus();
  }

  /**
   * Show short answer input
   */
  showShortAnswerInput() {
    document.getElementById('short-answer-input').classList.remove('hidden');
    document.getElementById('short-answer').value = '';
    document.getElementById('short-answer').focus();
  }

  /**
   * Handle option selection for multiple choice
   */
  selectOption(e) {
    const selectedBtn = e.target;
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');
    
    // Remove selection from all buttons
    buttons.forEach(btn => btn.classList.remove('selected'));
    
    // Add selection to clicked button
    selectedBtn.classList.add('selected');
    
    // Enable next button
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Handle numeric input
   */
  handleNumericInput() {
    const input = document.getElementById('numeric-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Handle short answer input
   */
  handleShortAnswerInput() {
    const input = document.getElementById('short-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Move to next question
   */
  nextQuestion() {
    this.saveCurrentAnswer();
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Skip current question
   */
  skipQuestion() {
    this.saveCurrentAnswer(''); // Save empty answer
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Save the current answer
   */
  saveCurrentAnswer(forcedAnswer = null) {
    const question = this.questions[this.currentQuestionIndex];
    let answer = forcedAnswer;
    
    if (answer === null) {
      if (question.type === 'multiple-choice') {
        const selectedBtn = document.querySelector('.option-btn.selected');
        answer = selectedBtn ? selectedBtn.textContent : '';
      } else if (question.type === 'numeric') {
        answer = document.getElementById('numeric-answer').value.trim();
      } else if (question.type === 'short-answer') {
        answer = document.getElementById('short-answer').value.trim();
      }
    }
    
    this.answers[this.currentQuestionIndex] = {
      questionId: question.id,
      questionType: question.type,
      topic: question.topic,
      studentAnswer: answer,
      timeSpent: new Date() - this.questionStartTimes[this.currentQuestionIndex]
    };
  }

  /**
   * Complete the assessment
   */
  async completeAssessment() {
    try {
      // Show loading
      this.showLoading('Processing your assessment...');
      
      // Stop timer
      this.stopTimer();
      
      // Submit assessment
      const results = await this.submitAssessment();
      
      // Hide loading and show results
      this.hideLoading();
      this.showResults(results);
      
    } catch (error) {
      console.error('Error completing assessment:', error);
      this.hideLoading();
      alert('Failed to complete assessment. Please try again.');
    }
  }

  /**
   * Submit assessment for grading
   */
  async submitAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const timeSpent = this.timeLimit - this.timeRemaining;

      const response = await fetch(`${baseUrl}/api/math-assessments/${this.assessmentId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          answers: this.answers,
          email: this.userData.email,
          level: this.currentLevel,
          timeSpent: timeSpent
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit assessment: ${response.status}`);
      }

      const results = await response.json();
      
      console.log('Assessment submitted successfully:', results);
      return results;

    } catch (error) {
      console.error('Error submitting assessment:', error);
      throw error;
    }
  }

  /**
   * Show assessment results
   */
  showResults(results) {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only results container
    document.getElementById('assessment-results').classList.remove('hidden');

    // Ensure results container is scrollable and focused for accessibility
    const resultsContainer = document.querySelector('.results-container');
    if (resultsContainer) {
      resultsContainer.setAttribute('tabindex', '0');
      setTimeout(() => {
        resultsContainer.focus();
        resultsContainer.scrollTop = 0; // Scroll to top of results
      }, 100);
    }

    // Update results display
    document.getElementById('final-score').textContent = results.score;
    document.getElementById('max-score').textContent = results.maxScore;

    // Update status with encouraging, level-based messaging
    const statusElement = document.getElementById('status-text');
    const statusBadge = statusElement.parentElement;

    // Clear previous status classes
    statusBadge.classList.remove('passed', 'not-passed', 'current-level', 'progress-made');

    if (results.passed) {
      statusElement.textContent = `${this.currentLevel} Level Complete!`;
      statusBadge.classList.add('passed');

      // Check if user can progress to next level
      this.checkLevelProgression(results);
    } else {
      // Show current achievement level instead of "Not Passed"
      const achievementLevel = this.determineAchievementLevel(results.score, results.maxScore);
      statusElement.textContent = `${achievementLevel.level} Achievement`;
      statusBadge.classList.add(achievementLevel.class);

      // Add encouraging subtitle
      this.addEncouragingMessage(achievementLevel, results);
    }

    // Show topic breakdown if available
    if (results.topicBreakdown) {
      this.displayTopicBreakdown(results.topicBreakdown);
    }

    // Show detailed feedback if available
    if (results.feedback) {
      this.displayFeedback(results.feedback);
    }

    // Show strengths and improvements
    if (results.strengths || results.improvements) {
      this.displayStrengthsAndImprovements(results.strengths, results.improvements);
    }

    // Show recommendations if available
    if (results.placementRecommendation) {
      this.displayRecommendations(results.placementRecommendation);
    }

    // Add progression options if applicable
    this.addProgressionOptions(results);
  }

  /**
   * Check if user can progress to next level
   */
  checkLevelProgression(results) {
    const progressionMap = {
      'Entry': 'Level1',
      'Level1': 'GCSEPart1',
      'GCSEPart1': 'GCSEPart2',
      'GCSEPart2': null // No further progression
    };

    const nextLevel = progressionMap[this.currentLevel];

    if (nextLevel && results.passed) {
      this.nextAvailableLevel = nextLevel;
      console.log(`User passed ${this.currentLevel}, can progress to ${nextLevel}`);
    }
  }

  /**
   * Add progression options to results
   */
  addProgressionOptions(results) {
    const actionsContainer = document.querySelector('.results-actions');

    // Clear existing actions
    actionsContainer.innerHTML = '';

    // Add detailed report button
    const reportBtn = document.createElement('button');
    reportBtn.id = 'view-detailed-report-btn';
    reportBtn.className = 'modern-submit-btn';
    reportBtn.innerHTML = `
      <span class="btn-text">View Detailed Report</span>
      <span class="btn-icon">📊</span>
    `;
    reportBtn.addEventListener('click', () => this.viewDetailedReport());
    actionsContainer.appendChild(reportBtn);

    // Add progression button if user passed and next level is available
    if (results.passed && this.nextAvailableLevel) {
      const progressBtn = document.createElement('button');
      progressBtn.className = 'modern-submit-btn progression-btn';
      progressBtn.innerHTML = `
        <span class="btn-text">Continue to ${this.nextAvailableLevel}</span>
        <span class="btn-icon">→</span>
      `;
      progressBtn.addEventListener('click', () => this.progressToNextLevel());
      actionsContainer.appendChild(progressBtn);
    }

    // Add retake button if user didn't pass
    if (!results.passed) {
      const retakeBtn = document.createElement('button');
      retakeBtn.className = 'modern-submit-btn retake-btn';
      retakeBtn.innerHTML = `
        <span class="btn-text">Retake Assessment</span>
        <span class="btn-icon">🔄</span>
      `;
      retakeBtn.addEventListener('click', () => this.retakeAssessment());
      actionsContainer.appendChild(retakeBtn);
    }
  }

  /**
   * Progress to next level
   */
  async progressToNextLevel() {
    if (!this.nextAvailableLevel) {
      alert('No next level available');
      return;
    }

    try {
      // Update current level
      this.currentLevel = this.nextAvailableLevel;
      this.nextAvailableLevel = null;

      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;

      // Show instructions for new level (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error progressing to next level:', error);
      alert('Failed to progress to next level. Please try again.');
    }
  }

  /**
   * Retake current assessment
   */
  async retakeAssessment() {
    try {
      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.timeRemaining = this.timeLimit;

      // Show instructions (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error retaking assessment:', error);
      alert('Failed to restart assessment. Please try again.');
    }
  }

  /**
   * View detailed report
   */
  async viewDetailedReport() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3000'
        : window.location.origin;

      const response = await fetch(`${baseUrl}/api/math-assessments/${this.userData.email}/report`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch report: ${response.status}`);
      }

      const reportData = await response.json();

      // Display detailed report (could open in new window or modal)
      this.displayDetailedReport(reportData.data);

    } catch (error) {
      console.error('Error fetching detailed report:', error);
      alert('Failed to load detailed report. Please try again.');
    }
  }

  /**
   * Display detailed report
   */
  displayDetailedReport(reportData) {
    // Create a modal or new page to show detailed report
    // For now, just log the data
    console.log('Detailed Report:', reportData);

    // You could implement a modal here to show:
    // - Complete assessment history
    // - Detailed topic breakdown
    // - Learning recommendations
    // - Progress tracking

    alert('Detailed report functionality would be implemented here. Check console for data.');
  }

  /**
   * Display topic performance breakdown
   */
  displayTopicBreakdown(topicBreakdown) {
    const container = document.getElementById('topic-breakdown');
    container.innerHTML = '';

    // Calculate total possible points for percentage calculation
    const totalTopics = Object.keys(topicBreakdown).length;
    const maxPointsPerTopic = Math.floor(this.levelSpecs[this.currentLevel].maxScore / totalTopics);

    Object.entries(topicBreakdown).forEach(([topic, score]) => {
      const percentage = Math.round((score / maxPointsPerTopic) * 100);
      const topicElement = document.createElement('div');
      topicElement.className = 'topic-item';

      // Add performance indicator
      let performanceClass = 'low';
      if (percentage >= 80) performanceClass = 'high';
      else if (percentage >= 60) performanceClass = 'medium';

      topicElement.innerHTML = `
        <div class="topic-info">
          <span class="topic-name">${this.formatTopicName(topic)}</span>
          <span class="topic-percentage ${performanceClass}">${percentage}%</span>
        </div>
        <div class="topic-progress-bar">
          <div class="topic-progress-fill ${performanceClass}" style="width: ${percentage}%"></div>
        </div>
        <span class="topic-score">${score}/${maxPointsPerTopic}</span>
      `;
      container.appendChild(topicElement);
    });
  }

  /**
   * Display placement recommendations
   */
  displayRecommendations(recommendation) {
    const container = document.getElementById('recommendations');
    container.innerHTML = `
      <div class="recommendation-header">
        <div class="recommendation-level">
          <span class="level-badge">${recommendation.level}</span>
          <span class="level-label">Recommended Level</span>
        </div>
      </div>

      <div class="recommendation-content">
        <div class="recommendation-reasoning">
          <h4>Assessment Summary</h4>
          <p>${recommendation.reasoning}</p>
        </div>

        <div class="recommendation-next-steps">
          <h4>Immediate Next Steps</h4>
          <ul class="next-steps-list">
            ${recommendation.nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>

        <div class="recommendation-courses">
          <h4>Recommended Courses</h4>
          <div class="course-list">
            ${recommendation.courseRecommendations.map(course => `
              <div class="course-item">
                <span class="course-icon">📚</span>
                <span class="course-name">${course}</span>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Display comprehensive feedback
   */
  displayFeedback(feedback) {
    const feedbackSection = document.querySelector('.feedback-section');

    if (!feedback || Object.keys(feedback).length === 0) {
      feedbackSection.style.display = 'none';
      return;
    }

    const feedbackContainer = document.createElement('div');
    feedbackContainer.className = 'feedback-container';

    const feedbackAreas = [
      { key: 'numericalSkills', title: 'Numerical Skills', icon: '🔢' },
      { key: 'algebraicThinking', title: 'Algebraic Thinking', icon: '📐' },
      { key: 'problemSolving', title: 'Problem Solving', icon: '🧩' },
      { key: 'geometricReasoning', title: 'Geometric Reasoning', icon: '📏' },
      { key: 'dataHandling', title: 'Data Handling', icon: '📊' }
    ];

    feedbackAreas.forEach(area => {
      if (feedback[area.key]) {
        const feedbackItem = document.createElement('div');
        feedbackItem.className = 'feedback-item';
        feedbackItem.innerHTML = `
          <div class="feedback-header">
            <span class="feedback-icon">${area.icon}</span>
            <h4 class="feedback-title">${area.title}</h4>
          </div>
          <p class="feedback-text">${feedback[area.key]}</p>
        `;
        feedbackContainer.appendChild(feedbackItem);
      }
    });

    // Add overall feedback if available
    if (feedback.overall) {
      const overallFeedback = document.createElement('div');
      overallFeedback.className = 'feedback-item overall-feedback';
      overallFeedback.innerHTML = `
        <div class="feedback-header">
          <span class="feedback-icon">🎯</span>
          <h4 class="feedback-title">Overall Assessment</h4>
        </div>
        <p class="feedback-text">${feedback.overall}</p>
      `;
      feedbackContainer.appendChild(overallFeedback);
    }

    // Replace existing content
    feedbackSection.innerHTML = '<h3>Your Performance Analysis</h3>';
    feedbackSection.appendChild(feedbackContainer);
  }

  /**
   * Display strengths and improvements
   */
  displayStrengthsAndImprovements(strengths, improvements) {
    const container = document.querySelector('.feedback-section');

    if (strengths && strengths.length > 0) {
      const strengthsDiv = document.createElement('div');
      strengthsDiv.className = 'strengths-section';
      strengthsDiv.innerHTML = `
        <h4 class="section-title positive">✅ Your Strengths</h4>
        <ul class="strengths-list">
          ${strengths.map(strength => `<li class="strength-item">${strength}</li>`).join('')}
        </ul>
      `;
      container.appendChild(strengthsDiv);
    }

    if (improvements && improvements.length > 0) {
      const improvementsDiv = document.createElement('div');
      improvementsDiv.className = 'improvements-section';
      improvementsDiv.innerHTML = `
        <h4 class="section-title improvement">📈 Areas for Development</h4>
        <ul class="improvements-list">
          ${improvements.map(improvement => `<li class="improvement-item">${improvement}</li>`).join('')}
        </ul>
      `;
      container.appendChild(improvementsDiv);
    }
  }

  /**
   * Format topic name for display
   */
  formatTopicName(topic) {
    return topic.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  /**
   * Determine achievement level based on score percentage
   */
  determineAchievementLevel(score, maxScore) {
    const percentage = (score / maxScore) * 100;

    if (percentage >= 80) {
      return { level: 'Excellent Progress', class: 'excellent-progress' };
    } else if (percentage >= 60) {
      return { level: 'Good Progress', class: 'good-progress' };
    } else if (percentage >= 40) {
      return { level: 'Steady Progress', class: 'steady-progress' };
    } else if (percentage >= 20) {
      return { level: 'Foundation Building', class: 'foundation-building' };
    } else {
      return { level: 'Getting Started', class: 'getting-started' };
    }
  }

  /**
   * Add encouraging message based on achievement level
   */
  addEncouragingMessage(achievementLevel, results) {
    // Create or update encouraging message element
    let messageElement = document.getElementById('encouraging-message');
    if (!messageElement) {
      messageElement = document.createElement('div');
      messageElement.id = 'encouraging-message';
      messageElement.className = 'encouraging-message';

      // Insert after status badge
      const statusBadge = document.getElementById('pass-status');
      statusBadge.parentNode.insertBefore(messageElement, statusBadge.nextSibling);
    }

    // Generate encouraging message based on performance
    const messages = {
      'excellent-progress': [
        'Outstanding work! You\'re demonstrating strong mathematical skills.',
        'You\'re very close to mastering this level. Keep up the excellent progress!'
      ],
      'good-progress': [
        'Great job! You\'re making solid progress in your mathematical journey.',
        'You\'ve shown good understanding. A bit more practice will get you to the next level!'
      ],
      'steady-progress': [
        'Well done! You\'re building a solid foundation in mathematics.',
        'You\'re on the right track. Continue practicing to strengthen your skills!'
      ],
      'foundation-building': [
        'Good start! You\'re developing important mathematical foundations.',
        'Every step counts in your learning journey. Keep building those skills!'
      ],
      'getting-started': [
        'You\'ve taken the first step in your mathematical journey!',
        'Learning mathematics takes time. You\'re building important foundations!'
      ]
    };

    const levelMessages = messages[achievementLevel.class] || messages['getting-started'];
    const randomMessage = levelMessages[Math.floor(Math.random() * levelMessages.length)];

    messageElement.innerHTML = `
      <div class="message-content">
        <span class="message-icon">🌟</span>
        <p class="message-text">${randomMessage}</p>
      </div>
    `;
  }

  /**
   * Start the assessment timer
   */
  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();
      
      if (this.timeRemaining <= 0) {
        this.completeAssessment();
      }
    }, 1000);
  }

  /**
   * Stop the assessment timer
   */
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update timer display
   */
  updateTimerDisplay() {
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('timer').textContent = timeString;
    document.getElementById('timer-display').textContent = timeString;
  }

  /**
   * Show loading screen
   */
  showLoading(message) {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show loading screen
    document.getElementById('assessment-loading').classList.remove('hidden');
    document.querySelector('.loading-text').textContent = message;
  }

  /**
   * Hide loading screen
   */
  hideLoading() {
    document.getElementById('assessment-loading').classList.add('hidden');
  }

  /**
   * Hide user form
   */
  hideUserForm() {
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.add('hidden');
    }
  }

  /**
   * Test method to verify container visibility flow
   * Can be called from browser console: window.mathAssessment.testContainerFlow()
   */
  testContainerFlow() {
    console.log('Testing container visibility flow...');

    const containers = [
      'user-form-container',
      'math-assessment-container',
      'assessment-instructions',
      'assessment-questions',
      'assessment-results',
      'assessment-loading',
      'header'
    ];

    const getVisibilityState = () => {
      const state = {};
      containers.forEach(id => {
        const element = document.getElementById(id);
        state[id] = element ? !element.classList.contains('hidden') : 'not found';
      });
      return state;
    };

    console.log('1. Initial state:', getVisibilityState());

    // Test reset to initial state
    this.resetToInitialState();
    console.log('2. After resetToInitialState():', getVisibilityState());

    // Test show instructions
    this.currentLevel = 'Entry';
    this.showInstructions();
    console.log('3. After showInstructions():', getVisibilityState());

    // Test show loading
    this.showLoading('Test loading...');
    console.log('4. After showLoading():', getVisibilityState());

    // Test hide loading and show questions
    this.hideLoading();
    this.showQuestions();
    console.log('5. After showQuestions():', getVisibilityState());

    // Reset back to initial state
    this.resetToInitialState();
    console.log('6. Final reset to initial state:', getVisibilityState());

    console.log('Container flow test completed!');
  }

  /**
   * Test responsive results display
   * Can be called from browser console: window.mathAssessment.testResponsiveResults()
   */
  testResponsiveResults() {
    console.log('Testing responsive results display...');

    // Create mock results data for testing (you can modify score to test different achievement levels)
    const mockResults = {
      score: 18, // Try different values: 35+ (passed), 25-34 (excellent), 15-24 (good), 10-14 (steady), 5-9 (foundation), 0-4 (getting started)
      maxScore: 44,
      passed: false, // Will be determined by score vs passing threshold
      topicBreakdown: {
        arithmetic: 8,
        algebra: 6,
        geometry: 4,
        statistics: 5,
        problemSolving: 3,
        measurement: 2
      },
      feedback: {
        numericalSkills: 'Strong performance in basic arithmetic operations. Shows good understanding of number relationships and calculation accuracy.',
        algebraicThinking: 'Demonstrates solid grasp of algebraic concepts. Can solve simple equations and work with variables effectively.',
        problemSolving: 'Good analytical approach to word problems. Shows ability to break down complex scenarios into manageable steps.',
        geometricReasoning: 'Basic understanding of geometric shapes and properties. Could benefit from more practice with angle calculations.',
        dataHandling: 'Competent in reading and interpreting basic charts and graphs. Shows understanding of averages and data trends.',
        overall: 'Excellent overall performance demonstrating readiness for Level 1 mathematics. Strong foundation across multiple topic areas with particular strength in arithmetic and algebra.'
      },
      strengths: [
        'Excellent arithmetic calculation skills',
        'Strong algebraic reasoning and equation solving',
        'Good problem-solving methodology',
        'Effective time management during assessment'
      ],
      improvements: [
        'Practice geometric angle calculations',
        'Develop statistical analysis skills',
        'Work on complex word problem interpretation',
        'Strengthen measurement unit conversions'
      ],
      placementRecommendation: {
        level: 'Level 1',
        reasoning: 'Strong performance across multiple mathematical areas indicates readiness for Level 1 mathematics course. Particular strengths in arithmetic and algebra provide excellent foundation for progression.',
        nextSteps: [
          'Enroll in Level 1 Mathematics course',
          'Focus on geometry and statistics modules',
          'Practice advanced problem-solving techniques'
        ],
        courseRecommendations: [
          'Functional Skills Mathematics Level 1',
          'GCSE Mathematics Foundation Tier',
          'Adult Numeracy Level 1 Course'
        ]
      }
    };

    // Show the results with mock data
    this.showResults(mockResults);

    console.log('Mock results displayed. Check responsive behavior:');
    console.log('1. Results container should have scrolling if content exceeds viewport');
    console.log('2. All sections should be accessible through scrolling');
    console.log('3. Layout should adapt to screen size');
    console.log('4. Action buttons should be visible and accessible');

    // Test viewport dimensions
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio
    };

    console.log('Current viewport:', viewport);

    if (viewport.width < 768) {
      console.log('📱 Mobile viewport detected - verify mobile-specific styles');
    } else if (viewport.width < 1024) {
      console.log('📱 Tablet viewport detected - verify tablet-specific styles');
    } else {
      console.log('🖥️ Desktop viewport detected - verify desktop layout');
    }

    return mockResults;
  }
}
