<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Mathematics Components Test</title>
    
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- MathQuill -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: white;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .test-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .performance-metrics {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .metric:last-child {
            margin-bottom: 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-pass {
            background: #10b981;
        }
        
        .status-fail {
            background: #ef4444;
        }
        
        .status-pending {
            background: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-3xl font-bold text-center mb-8">Interactive Mathematics Components Test Suite</h1>
        
        <!-- Number Line Test -->
        <div class="test-section">
            <h2 class="test-title">Number Line Slider Component</h2>
            <p class="test-description">Test the interactive number line with draggable markers and keyboard navigation.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="testNumberLine()">Test Number Line</button>
                <button class="test-btn" onclick="testNumberLineKeyboard()">Test Keyboard Navigation</button>
                <button class="test-btn" onclick="testNumberLineAccessibility()">Test Accessibility</button>
            </div>
            
            <div id="number-line-test-area">
                <!-- Number line will be inserted here -->
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <span>Component Load Time:</span>
                    <span id="number-line-load-time">-</span>
                </div>
                <div class="metric">
                    <span>Interaction Response:</span>
                    <span id="number-line-response-time">-</span>
                </div>
                <div class="metric">
                    <span>Accessibility Score:</span>
                    <span id="number-line-accessibility">-</span>
                </div>
            </div>
        </div>
        
        <!-- Drag and Drop Test -->
        <div class="test-section">
            <h2 class="test-title">Drag and Drop Puzzle System</h2>
            <p class="test-description">Test drag-and-drop functionality with mathematical elements and keyboard support.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="testDragDrop()">Test Drag & Drop</button>
                <button class="test-btn" onclick="testDragDropKeyboard()">Test Keyboard Support</button>
                <button class="test-btn" onclick="testDragDropValidation()">Test Validation</button>
            </div>
            
            <div id="drag-drop-test-area">
                <!-- Drag and drop will be inserted here -->
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <span>Component Load Time:</span>
                    <span id="drag-drop-load-time">-</span>
                </div>
                <div class="metric">
                    <span>Drag Performance:</span>
                    <span id="drag-drop-performance">-</span>
                </div>
                <div class="metric">
                    <span>Keyboard Support:</span>
                    <span id="drag-drop-keyboard">-</span>
                </div>
            </div>
        </div>
        
        <!-- Equation Builder Test -->
        <div class="test-section">
            <h2 class="test-title">Equation Builder with MathQuill</h2>
            <p class="test-description">Test the mathematical equation editor with symbol toolbar and LaTeX output.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="testEquationBuilder()">Test Equation Builder</button>
                <button class="test-btn" onclick="testMathQuillIntegration()">Test MathQuill Integration</button>
                <button class="test-btn" onclick="testEquationValidation()">Test Validation</button>
            </div>
            
            <div id="equation-builder-test-area">
                <!-- Equation builder will be inserted here -->
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <span>MathQuill Load Time:</span>
                    <span id="mathquill-load-time">-</span>
                </div>
                <div class="metric">
                    <span>Rendering Performance:</span>
                    <span id="equation-render-time">-</span>
                </div>
                <div class="metric">
                    <span>LaTeX Output Quality:</span>
                    <span id="latex-quality">-</span>
                </div>
            </div>
        </div>
        
        <!-- Coordinate Grid Test -->
        <div class="test-section">
            <h2 class="test-title">Interactive Coordinate Grid</h2>
            <p class="test-description">Test point plotting, line drawing, and keyboard navigation on coordinate plane.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="testCoordinateGrid()">Test Grid Interaction</button>
                <button class="test-btn" onclick="testGridKeyboard()">Test Keyboard Navigation</button>
                <button class="test-btn" onclick="testGridTools()">Test Drawing Tools</button>
            </div>
            
            <div id="coordinate-grid-test-area">
                <!-- Coordinate grid will be inserted here -->
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <span>SVG Render Time:</span>
                    <span id="svg-render-time">-</span>
                </div>
                <div class="metric">
                    <span>Point Plot Performance:</span>
                    <span id="point-plot-performance">-</span>
                </div>
                <div class="metric">
                    <span>Keyboard Navigation:</span>
                    <span id="grid-keyboard-nav">-</span>
                </div>
            </div>
        </div>
        
        <!-- Visual Manipulatives Test -->
        <div class="test-section">
            <h2 class="test-title">Visual Manipulatives System</h2>
            <p class="test-description">Test interactive fraction bars, geometric shapes, and counting blocks.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="testVisualManipulatives()">Test Manipulatives</button>
                <button class="test-btn" onclick="testFractionBars()">Test Fraction Bars</button>
                <button class="test-btn" onclick="testManipulativesAccessibility()">Test Accessibility</button>
            </div>
            
            <div id="visual-manipulatives-test-area">
                <!-- Visual manipulatives will be inserted here -->
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <span>Component Load Time:</span>
                    <span id="manipulatives-load-time">-</span>
                </div>
                <div class="metric">
                    <span>Interaction Response:</span>
                    <span id="manipulatives-response">-</span>
                </div>
                <div class="metric">
                    <span>State Management:</span>
                    <span id="manipulatives-state">-</span>
                </div>
            </div>
        </div>
        
        <!-- Overall Performance Summary -->
        <div class="test-section">
            <h2 class="test-title">Performance Summary</h2>
            <p class="test-description">Overall performance metrics and recommendations.</p>
            
            <div class="test-controls">
                <button class="test-btn" onclick="runAllTests()">Run All Tests</button>
                <button class="test-btn" onclick="generateReport()">Generate Report</button>
                <button class="test-btn" onclick="exportResults()">Export Results</button>
            </div>
            
            <div id="performance-summary" class="performance-metrics">
                <div class="metric">
                    <span>Total Load Time:</span>
                    <span id="total-load-time">-</span>
                </div>
                <div class="metric">
                    <span>Average Response Time:</span>
                    <span id="avg-response-time">-</span>
                </div>
                <div class="metric">
                    <span>Accessibility Compliance:</span>
                    <span id="accessibility-compliance">-</span>
                </div>
                <div class="metric">
                    <span>Browser Compatibility:</span>
                    <span id="browser-compatibility">-</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="validationFunctions.js"></script>
    <script src="mathInteractiveComponents.js"></script>
    <script src="test_interactive_math.js"></script>
</body>
</html>
